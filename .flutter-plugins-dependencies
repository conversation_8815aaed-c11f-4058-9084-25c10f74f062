{"info": "This is a generated file; do not edit or check into version control.", "plugins": {"ios": [{"name": "aion_sdk", "path": "/Users/<USER>/fvm/versions/*******+hotfix.4.fliggy/packages/aion_sdk/", "native_build": true, "dependencies": ["flutter_boost"]}, {"name": "amap_flutter_map", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_basic_flutter_map-44894ae1e90bd5b7f0d5fd826364fd378257cd9c/", "native_build": true, "dependencies": []}, {"name": "device_info", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/device_info-2.0.3/", "native_build": true, "dependencies": []}, {"name": "fbridge", "path": "/Users/<USER>/.faion/pub-cache/git/fbridge-2c04aa5d1098fc9e9aab0df553711ecb8225565c/", "native_build": true, "dependencies": ["fliggy_router"]}, {"name": "ffperformance", "path": "/Users/<USER>/.faion/pub-cache/git/ffperformance-6e618e5c97ba45703a282c85d3635bc379c25559/", "native_build": true, "dependencies": []}, {"name": "ficonfont", "path": "/Users/<USER>/.faion/pub-cache/git/ficonfont-dd23c47da3b40ee64f6e51b9f46ee64d92f32b6d/", "native_build": true, "dependencies": ["path_provider"]}, {"name": "fliggy_config", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_config-ca3ca5963e9dcd18d07e847a3772867395e3e53f/", "native_build": true, "dependencies": ["fbridge"]}, {"name": "fliggy_flutter_location", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_location-5f27338ec0118d081a74283cd729ac92406b6411/", "native_build": true, "dependencies": ["fbridge", "fliggy_router"]}, {"name": "fliggy_flutter_login", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_login-4484faf72c7b3bb517e32e35e951bd8821b8dafe/", "native_build": true, "dependencies": []}, {"name": "fliggy_flutter_map", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_flutter_map-b162384a89d0e2d38b3b18e41c393db670fa1548/", "native_build": true, "dependencies": ["aion_sdk", "amap_flutter_map", "fliggy_map_search", "package_info", "path_provider", "url_launcher", "fliggy_flutter_storage", "fliggy_config"]}, {"name": "fliggy_flutter_storage", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_storage-b2abe4b86222ea82eb189ae42dae036581ee0016/", "native_build": true, "dependencies": ["shared_preferences", "fbridge"]}, {"name": "fliggy_map_search", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_map_search-61e2febc40b924ed4f304f62bc35b37052a4a4df/", "native_build": true, "dependencies": []}, {"name": "fliggy_router", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_router-db61d840180a3733bd54c3cf625fb6653a40946e/", "native_build": true, "dependencies": []}, {"name": "fliggy_usertrack", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_usertrack-791fc6ca22ca4f48c6a9847805b60a51384ba498/", "native_build": true, "dependencies": ["fbridge"]}, {"name": "fliggy_webview", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_webview-3b11ca49071ab9140e9620d7f249f41352c8a412/", "native_build": true, "dependencies": ["flutter_boost"]}, {"name": "flutter_boost", "path": "/Users/<USER>/.faion/pub-cache/git/flutter_boost-6a367bea1522312dff139f5bb2c21b44ede6fcc4/", "native_build": true, "dependencies": []}, {"name": "ftitlebar_adapter", "path": "/Users/<USER>/.faion/pub-cache/git/ftitlebar_adapter-8f80e329b6c5550c76d85dd5857215ccb1d70d67/", "native_build": true, "dependencies": ["fbridge", "ficonfont"]}, {"name": "package_info", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/package_info-2.0.2/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.faion/pub-cache/git/package_info_plus-3598ce68def47fea89afeeea66343ae848012a87/", "native_build": true, "dependencies": []}, {"name": "path_provider", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/path_provider-2.0.6/", "native_build": true, "dependencies": []}, {"name": "shared_preferences", "path": "/Users/<USER>/.faion/pub-cache/git/shared_preferences-4de85dc664825f2c04b9a1c44357dc3cd930e3f9/", "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/sqflite-2.2.8+4/", "native_build": true, "dependencies": []}, {"name": "url_launcher", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/url_launcher-6.0.10/", "native_build": true, "dependencies": []}], "android": [{"name": "aion_sdk", "path": "/Users/<USER>/fvm/versions/*******+hotfix.4.fliggy/packages/aion_sdk/", "native_build": true, "dependencies": ["flutter_boost"]}, {"name": "amap_flutter_map", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_basic_flutter_map-44894ae1e90bd5b7f0d5fd826364fd378257cd9c/", "native_build": true, "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "device_info", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/device_info-2.0.3/", "native_build": true, "dependencies": []}, {"name": "ffperformance", "path": "/Users/<USER>/.faion/pub-cache/git/ffperformance-6e618e5c97ba45703a282c85d3635bc379c25559/", "native_build": true, "dependencies": []}, {"name": "fliggy_config", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_config-ca3ca5963e9dcd18d07e847a3772867395e3e53f/", "native_build": true, "dependencies": []}, {"name": "fliggy_flutter_storage", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_storage-b2abe4b86222ea82eb189ae42dae036581ee0016/", "native_build": true, "dependencies": ["shared_preferences"]}, {"name": "fliggy_map_search", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_map_search-61e2febc40b924ed4f304f62bc35b37052a4a4df/", "native_build": true, "dependencies": []}, {"name": "fliggy_webview", "path": "/Users/<USER>/.faion/pub-cache/git/fliggy_webview-3b11ca49071ab9140e9620d7f249f41352c8a412/", "native_build": true, "dependencies": ["flutter_boost"]}, {"name": "flutter_boost", "path": "/Users/<USER>/.faion/pub-cache/git/flutter_boost-6a367bea1522312dff139f5bb2c21b44ede6fcc4/", "native_build": true, "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/flutter_plugin_android_lifecycle-2.0.17/", "native_build": true, "dependencies": []}, {"name": "package_info", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/package_info-2.0.2/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.faion/pub-cache/git/package_info_plus-3598ce68def47fea89afeeea66343ae848012a87/", "native_build": true, "dependencies": []}, {"name": "path_provider", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/path_provider-2.0.6/", "native_build": true, "dependencies": []}, {"name": "shared_preferences", "path": "/Users/<USER>/.faion/pub-cache/git/shared_preferences-4de85dc664825f2c04b9a1c44357dc3cd930e3f9/", "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/sqflite-2.2.8+4/", "native_build": true, "dependencies": []}, {"name": "url_launcher", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/url_launcher-6.0.10/", "native_build": true, "dependencies": []}], "macos": [{"name": "package_info", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/package_info-2.0.2/", "native_build": true, "dependencies": []}, {"name": "package_info_plus", "path": "/Users/<USER>/.faion/pub-cache/git/package_info_plus-3598ce68def47fea89afeeea66343ae848012a87/", "native_build": true, "dependencies": []}, {"name": "path_provider_macos", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/path_provider_macos-2.0.7/", "native_build": true, "dependencies": []}, {"name": "shared_preferences_macos", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/shared_preferences_macos-2.0.5/", "native_build": true, "dependencies": []}, {"name": "sqflite", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/sqflite-2.2.8+4/", "native_build": true, "dependencies": []}, {"name": "url_launcher_macos", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/url_launcher_macos-2.0.3/", "native_build": true, "dependencies": []}], "linux": [{"name": "package_info_plus", "path": "/Users/<USER>/.faion/pub-cache/git/package_info_plus-3598ce68def47fea89afeeea66343ae848012a87/", "native_build": false, "dependencies": []}, {"name": "path_provider_linux", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/path_provider_linux-2.2.1/", "native_build": false, "dependencies": []}, {"name": "shared_preferences_linux", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/shared_preferences_linux-2.3.2/", "native_build": false, "dependencies": ["path_provider_linux"]}, {"name": "url_launcher_linux", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/url_launcher_linux-2.0.3/", "native_build": true, "dependencies": []}], "windows": [{"name": "package_info_plus", "path": "/Users/<USER>/.faion/pub-cache/git/package_info_plus-3598ce68def47fea89afeeea66343ae848012a87/", "native_build": false, "dependencies": []}, {"name": "path_provider_windows", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/path_provider_windows-2.2.1/", "native_build": false, "dependencies": []}, {"name": "shared_preferences_windows", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/shared_preferences_windows-2.3.2/", "native_build": false, "dependencies": ["path_provider_windows"]}, {"name": "url_launcher_windows", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/url_launcher_windows-2.0.2/", "native_build": true, "dependencies": []}], "web": [{"name": "package_info_plus", "path": "/Users/<USER>/.faion/pub-cache/git/package_info_plus-3598ce68def47fea89afeeea66343ae848012a87/", "dependencies": []}, {"name": "shared_preferences_web", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/shared_preferences_web-2.2.1/", "dependencies": []}, {"name": "url_launcher_web", "path": "/Users/<USER>/.faion/pub-cache/hosted/flutter.alibaba-inc.com/url_launcher_web-2.0.19/", "dependencies": []}]}, "dependencyGraph": [{"name": "aion_sdk", "dependencies": ["flutter_boost"]}, {"name": "amap_flutter_map", "dependencies": ["flutter_plugin_android_lifecycle"]}, {"name": "device_info", "dependencies": []}, {"name": "fbridge", "dependencies": ["fliggy_router"]}, {"name": "ffperformance", "dependencies": []}, {"name": "ficonfont", "dependencies": ["path_provider"]}, {"name": "fliggy_config", "dependencies": ["fbridge"]}, {"name": "fliggy_flutter_location", "dependencies": ["fbridge", "fliggy_router"]}, {"name": "fliggy_flutter_login", "dependencies": []}, {"name": "fliggy_flutter_map", "dependencies": ["aion_sdk", "amap_flutter_map", "fliggy_map_search", "package_info", "path_provider", "url_launcher", "fliggy_flutter_storage", "fliggy_config"]}, {"name": "fliggy_flutter_storage", "dependencies": ["shared_preferences", "fbridge"]}, {"name": "fliggy_map_search", "dependencies": []}, {"name": "fliggy_router", "dependencies": []}, {"name": "fliggy_usertrack", "dependencies": ["fbridge"]}, {"name": "fliggy_webview", "dependencies": ["flutter_boost"]}, {"name": "flutter_boost", "dependencies": []}, {"name": "flutter_plugin_android_lifecycle", "dependencies": []}, {"name": "ftitlebar_adapter", "dependencies": ["fbridge", "ficonfont"]}, {"name": "package_info", "dependencies": []}, {"name": "package_info_plus", "dependencies": []}, {"name": "path_provider", "dependencies": ["path_provider_linux", "path_provider_macos", "path_provider_windows"]}, {"name": "path_provider_linux", "dependencies": []}, {"name": "path_provider_macos", "dependencies": []}, {"name": "path_provider_windows", "dependencies": []}, {"name": "shared_preferences", "dependencies": ["shared_preferences_linux", "shared_preferences_macos", "shared_preferences_web", "shared_preferences_windows"]}, {"name": "shared_preferences_linux", "dependencies": ["path_provider_linux"]}, {"name": "shared_preferences_macos", "dependencies": []}, {"name": "shared_preferences_web", "dependencies": []}, {"name": "shared_preferences_windows", "dependencies": ["path_provider_windows"]}, {"name": "sqflite", "dependencies": []}, {"name": "url_launcher", "dependencies": ["url_launcher_linux", "url_launcher_macos", "url_launcher_web", "url_launcher_windows"]}, {"name": "url_launcher_linux", "dependencies": []}, {"name": "url_launcher_macos", "dependencies": []}, {"name": "url_launcher_web", "dependencies": []}, {"name": "url_launcher_windows", "dependencies": []}], "date_created": "2025-05-27 11:14:35.931277", "version": "3.7.3"}