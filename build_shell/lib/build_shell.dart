
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:aion_sdk/aion_sdk.dart';
import 'package:fliggy_flutter_community/fliggy_flutter_community.dart' as biz;

/// 页面在此处配置<br>
/// 页面名称规范：<业务>/<页面>，如 /fhotel/media_list
/// <br>必须遵从规范，否则无法使用 aion 离线包，以及进行内部页面跳转
Map<String, FliggyPageBuilder> pages = biz.pages;

bool _inited = false;

/// 该函数只有 AionBundle 场景下会被调用
@pragma('vm:entry-point')
@pragma('vm:aion_entry')
Map<String, AionPageBuilder> getRouteConfig() {
  if (!_inited) {
    _inited = true;
    biz.package = "fliggy_flutter_community_1748315677";
    biz.bundleVersion = "0.2.4";
    print("__${biz.package}_init__");
    print("packageName: ${biz.package}");
    biz.init();

  }
  Map<String, AionPageBuilder> map =
      pages.map((key, value) => MapEntry(key, (pageName, params) {
            Map newParams = params!;
            if (params["__flutter_arguments__"] != null) {
              try {
                newParams = json.decode(params["__flutter_arguments__"]!);
              } catch (e, s) {
                print("error[$e]: $s");
              }
            }
            // 增加ThemeData为了解决iOS字重不生效问题
            ThemeData? iosThemeData;
            if(Platform.isIOS){
             iosThemeData = ThemeData(
                                  fontFamilyFallback: ["PingFang SC"],);
            }
           if(iosThemeData == null){
               return value(pageName, newParams, newParams['__container_uniqueId_key__']);
           }else{
              return Theme(
                            data: iosThemeData,
                             child: value(
                                          pageName, newParams, newParams['__container_uniqueId_key__']),
                           );
           }



          }));
  return map;
}
        