name: fliggy_flutter_community_1748315677
description: A new Flutter plugin.

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 0.0.1

environment:
  sdk: ">=2.12.0 <3.0.0"

dependencies:
  aion_sdk:
    sdk: flutter
  fliggy_flutter_community:
    path: ../../fliggy_flutter_community
  flutter:
    sdk: flutter

dependency_overrides:
  archive:
    git:
      ref: 3.3.1
      url: **************************:fliggy_mobile/archive.git
  cached_network_image: 3.2.3
  dio: 4.0.6
  fbridge:
    git:
      ref: 3.2.22
      url: **************************:fliggy_mobile/fbridge.git
  fbridge_channel:
    git:
      ref: 1.2.0
      url: **************************:fliggy_mobile/fbridge_channel.git
  fbroadcast:
    git:
      ref: 1.3.0
      url: **************************:fapi/fbroadcast.git
  fcontrol:
    git:
      ref: 2.0.1
      url: **************************:fapi/fcontrol.git
  fdata_center:
    git:
      ref: 0.0.4
      url: **************************:fapi/fdata_center.git
  fdensity:
    git:
      ref: 2.1.2
      url: **************************:fapi/fdensity.git
  fexp:
    git:
      ref: master
      url: **************************:fapi/fexp.git
  ffliggykv_core:
    git:
      ref: 2.0.5
      url: **************************:fliggy_mobile/ffliggykv_core.git
  ffperformance:
    git:
      ref: 2.1.7
      url: **************************:fliggy_mobile/ffperformance.git
  ficonfont:
    git:
      ref: 1.3.3
      url: **************************:fliggy_mobile/ficonfont.git
  fimage:
    git:
      ref: 1.0.2
      url: **************************:fapi/fimage.git
  fliggy_app_info:
    git:
      ref: 1.0.7
      url: **************************:fliggy_mobile/fliggy_app_info.git
  fliggy_design: 0.0.4
  fliggy_flutter_location:
    git:
      ref: 0.2.4
      url: **************************:fliggy_mobile/fliggy_location.git
  fliggy_flutter_login:
    git:
      ref: 0.2.1
      url: **************************:fliggy_mobile/fliggy_login.git
  fliggy_flutter_map:
    git:
      ref: 0.4.24
      url: **************************:fliggy_mobile/fliggy_flutter_map.git
  fliggy_font:
    git:
      path: packages/fliggy_font
      ref: 0.2.0
      url: **************************:fliggy_mobile/fliggy_design.git
  fliggy_mtop:
    git:
      ref: 1.2.9
      url: **************************:fliggy_mobile/fliggy_mtop.git
  fliggy_router:
    git:
      ref: 2.1.22
      url: **************************:fliggy_mobile/fliggy_router.git
  fliggy_usertrack:
    git:
      ref: 2.0.6
      url: **************************:fliggy_mobile/fliggy_usertrack.git
  fliggy_webview:
    git:
      ref: 0.3.4
      url: **************************:fliggy_mobile/fliggy_webview.git
  flutter_boost:
    git:
      ref: 4.4.9
      url: **************************:fliggy_mobile/flutter_boost.git
  flutter_cache_manager: 3.3.0
  flutter_common:
    git:
      ref: 1.6.0
      url: **************************:fliggy_mobile/flutter_common.git
  flutter_fliggyKV:
    git:
      ref: 2.0.5
      url: **************************:fliggy_mobile/flutter_fliggyKV.git
  flutter_staggered_grid_view:
    git:
      ref: master
      url: **************************:fapi/flutter_staggered_grid_view.git
  fround_image:
    git:
      ref: 0.6.7
      url: **************************:fapi/fround_image.git
  fsuper:
    git:
      ref: 2.1.2
      url: **************************:fapi/fsuper.git
  ftitlebar:
    git:
      ref: 1.1.1
      url: **************************:fapi/ftitlebar.git
  ftitlebar_adapter:
    git:
      ref: 1.1.3
      url: **************************:fliggy_mobile/ftitlebar_adapter.git
  ftoast:
    git:
      ref: 2.0.1
      url: **************************:fapi/ftoast.git
  html: 0.15.0
  infinite_scroll_pagination:
    git:
      ref: 4.0.0
      url: **************************:fapi/infinite_scroll_pagination.git
  intl: 0.17.0
  lottie: 2.3.2
  package_info_plus:
    git:
      ref: 3598ce68def47fea89afeeea66343ae848012a87
      url: **************************:fliggy_mobile/package_info_plus.git
  path_provider: 2.0.6
  pull_to_refresh: 2.0.0
  quiver: 3.2.1
  shared_preferences:
    git:
      ref: 2.0.8
      url: **************************:fapi/shared_preferences.git
  tuple: 2.0.1

dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  assets:
    - assets/play_map/iOS/style.data
    - assets/play_map/iOS/style_extra.data
    - assets/play_map/Android/style.data
    - assets/play_map/Android/style_extra.data
    - assets/play_map/iOS_10/style.data
    - assets/play_map/iOS_10/style_extra.data
    - assets/play_map/Android_10/style.data
    - assets/play_map/Android_10/style_extra.data
    - assets/play_map/user_location_icon.png

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
            