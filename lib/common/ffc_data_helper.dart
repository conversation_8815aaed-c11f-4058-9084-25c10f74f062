/// @Package
/// @File ffc_data_helper
/// <AUTHOR>
/// @Description 数据助手处理
/// @Date 10-16-2024 周三 11:12

import 'dart:convert';
import 'dart:io';
import 'package:fliggy_app_info/fliggy_app_info.dart';
import 'package:fliggy_flutter_community/common/ffc_utils.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter_fliggyKV/fliggykv.dart';
import 'package:package_info/package_info.dart';

class Env {
  static const int Trip_Test = 0; // 日常
  static const int Trip_Prepare = 1; // RC1环境 预发
  static const int Trip_Release = 3; // 线上
}
class FfcDataHelper {
  //保存单例
  static final FfcDataHelper _singleton = FfcDataHelper._internal();
  //工厂构造函数
  factory FfcDataHelper() => _singleton;
  //私有构造函数
  FfcDataHelper._internal();


  Map? client;
  int env = Env.Trip_Release; // Trip_Release: 3

  FliggyKV? _fliggyKV;
  FliggyKV get fliggyKV {
    _fliggyKV ??= FliggyKV("com.fliggy.ffcommunity", componentPath: "/ffcommunity/data");
    return _fliggyKV!;
  }
  // FliggyKV get fliggyKV => FliggyKV("com.fliggy.ffcommunity", componentPath: "/ffcommunity/data");

  // 登录状态
  ValueNotifier<bool> loginState = ValueNotifier<bool>(false);

  /// 发布器Tag是否展示
  ValueNotifier<bool> isHasGuide = ValueNotifier<bool>(false);

  bool isIOS18_2Fix = false;

  bool is91018Padding = false;

  // 提供一个方法来初始化环境参数/用户信息
  Future<bool> getUserEnv() async {
    bool success = false;
    // 环境信息
    client = await FfcUtils.ffcBridge<Map>('client_info');
    env = client?['env'] ?? Env.Trip_Release;

    // 仅在飞猪版本 9.10.12 以下操作,
    String appVersion = await getAppVersion();

    /// 等量大了以后,这里可以不要了
    if (isVersionGreaterThan('9.10.12', appVersion)) {
      isIOS18_2Fix = await IOS18_2Fix();
    } else {
      isIOS18_2Fix = false;
    }

    if (isVersionGreaterThan('9.10.18', appVersion)) {
      is91018Padding = false;
    } else {
      is91018Padding = true;
    }
    return success;
  }

  /// 获取飞猪版本号
  Future<String> getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    String appVersion = packageInfo.version;
    if (Platform.isIOS) {
      appVersion = packageInfo.buildNumber;
    }
    return appVersion;
  }

  // iOS 18.2 问题修复， 需要判断系统版本 在iOS 18.1以上 修复问题（点击Tab切换后， H5点击事件失效）
  Future<bool> IOS18_2Fix() async {
    if (Platform.isIOS) {
      /// 获取手机系统版本
      String systemVersion = await FliggyAppInfoApi.getInstance().getSystemVersion();
      print('isIOS18_2Fix: systemVersion: $systemVersion');
      // 判断系统版本大于18.1以上
      if (isVersionGreaterThan(systemVersion, "18.1.99")) {
        print('isIOS18_2Fix:  true ');
        return true;
      }
      return false;
    }
    return false;
  }

  // 三位版本号比较大小 例如 是否大于 18.2.0
  bool isVersionGreaterThan(String version, String targetVersion) {
    print('isIOS18_2Fix: isVersionGreaterThan: version: $version, targetVersion: $targetVersion');
    List<String> versionList = version.split(".");
    List<String> targetVersionList = targetVersion.split(".");
    if (SafeAccess.isListEmpty(versionList) || SafeAccess.isListEmpty(targetVersionList)) {
      print('isIOS18_2Fix: isVersionGreaterThan: versionList: $versionList, targetVersionList: $targetVersionList');
      return false;
    }
    for (int i = 0; i < versionList.length; i++) {
      int versionNum = SafeAccess.safeParseInt(SafeAccess.safeGet<String>(list: versionList, index: i, defaultValue: '0'));
      int targetVersionNum = SafeAccess.safeParseInt(SafeAccess.safeGet<String>(list: targetVersionList, index: i, defaultValue: '0'));
      print('isIOS18_2Fix: isVersionGreaterThan: versionNum: $versionNum, targetVersionNum: $targetVersionNum');
      if (versionNum > targetVersionNum) {
        return true;
      } else if (versionNum < targetVersionNum) {
        return false;
      } else {
        continue;
      }
    }
    return false;
  }



  // 获取目的地页面链接
  String getDestinationUrl(String destId, {String? orangeUrl, bool showTab = false}) {
    String url = "";
    if (env == Env.Trip_Release) {
      url = "https://outfliggys.m.taobao.com/app/trip/rx-dest-2024/pages/detail?titleBarHidden=2&disableNav=YES&fpt=ftid($DESTINATION_FT_ID)&from=tab&destId=$destId";
    } else {
      url = "https://outfliggys.wapa.taobao.com/app/trip/rx-dest-2024/pages/detail?titleBarHidden=2&disableNav=YES&fpt=ftid($DESTINATION_FT_ID)&from=tab&destId=$destId";
    }
    if (!SafeAccess.isTextEmpty(orangeUrl)) {
      url = SafeAccess.safeParseString(orangeUrl);
    }
    // fpt更新
    return FfcUtils.updateUrlParams(url, {
      "destId": destId,
      "fpt": "ftid($DESTINATION_FT_ID)",
      "webViewBackgroundColor": "efefef",
      "_fli_use_manifest": "true",
      if(!showTab) "from": "tab"
    }, removeKeys: [(showTab ? "from" : "")]);
  }

  // 获取看世界页面链接
  String getWorldViewUrl({String? orangeUrl, bool showTab = false}) {
    String url = "";
    if (env == Env.Trip_Release) {
      url = "https://outfliggys.m.taobao.com/app/trip/rx-dest-trip-world/pages/home?disableNav=YES&titleBarHidden=2&from=tab";
    } else {
      url = "https://outfliggys.wapa.taobao.com/app/trip/rx-dest-trip-world/pages/home?disableNav=YES&titleBarHidden=2&from=tab";
    }
    if (!SafeAccess.isTextEmpty(orangeUrl)) {
      url = SafeAccess.safeParseString(orangeUrl);
    }
    // fpt更新
    return FfcUtils.updateUrlParams(url, {
      // "webViewBackgroundColor":"efefef",
      // "fpt": "ftid($DESTINATION_FT_ID)",
      if(!showTab) "from": "tab"
    }, removeKeys: [(showTab ? "from" : "")]);
  }

  // 获取城市选择页面链接
  String get getCitySelectUrl {
    if (env == Env.Trip_Release) {
      return "https://market.m.taobao.com/app/trip/rx-dest-city-choose/pages/home?titleBarHidden=2&disableNav=YES&_fli_background_transparent=true&fpt=ftid&from=tab";
    } else {
      return "https://market.wapa.taobao.com/app/trip/rx-dest-city-choose/pages/home?titleBarHidden=2&disableNav=YES&_fli_background_transparent=true&fpt=ftid&from=tab";
    }
  }

  // 获取猪搜页面链接
  String get getSearchUrl {
    if (env == Env.Trip_Release) {
      return "https://market.m.taobao.com/app/trip/rx-search-all/pages/home?flutter_search=true";
    } else {
      return "https://market.wapa.taobao.com/app/trip/rx-search-all/pages/home?flutter_search=true";
    }
  }



  /// 分享相关
  // 目的地分享
  Map getDestinationShare(String destName, String? picUrl) {
    String imgUrl = SafeAccess.safeParseString(picUrl, def: 'https://gw.alicdn.com/imgextra/i4/O1CN01gaT7z91BvFjyEawHg_!!6000000000007-2-tps-618-618.png');
    return {
      'content': '找特惠，挑路线，选景点，查攻略',
      'img_url': imgUrl,
      'title': '来飞猪，酷玩【$destName】',
    };
  }
  // 看世界分享
  Map getWorldViewShare(String? picUrl ) {
    String imgUrl = SafeAccess.safeParseString(picUrl, def: 'https://gw.alicdn.com/imgextra/i4/O1CN01gaT7z91BvFjyEawHg_!!6000000000007-2-tps-618-618.png');
    return {
      'content': '📍最新热点，时令玩法，发现全球好去处。',
      'img_url': imgUrl,
      'title': '🌏来飞猪，寻找旅行灵感！',
    };
  }

  /// ----------------------- 获取KVCache-----------------------

  // 获取KVCache
  // 目的地Key mdd_pic
  // 看世界Key trip_world
  static Future<String?> getSharePicUrl(FfcTab tab) async {
    if (tab == FfcTab.CITY) {
      return FfcUtils.ffcBridge<String?>('get_kvcache', arguments: {
        'key': 'mdd_pic'
      });
    } else if (tab == FfcTab.LOOK_WORLD) {
      return FfcUtils.ffcBridge<String?>('get_kvcache', arguments: {
        'key': 'trip_world'
      });
    }
    return null;
  }
  /// ----------------------- 本地持久化存储 -----------------------

  /// 本地持久化存储
  static void setStorageString(String key, String value) {
    FfcDataHelper().fliggyKV.encodeString(key, value);
  }
  // get
  static String? getStorageString(String key) {
    return FfcDataHelper().fliggyKV.decodeString(key);
  }

  static void setStorageAssemble<T>(String key, T value) {
    String valueString = jsonEncode(value);
    FfcDataHelper().fliggyKV.encodeString(key, valueString);
  }
  // get Assemble
  static T? getStorageAssemble<T>(String key) {
    String? valueString = FfcDataHelper().fliggyKV.decodeString(key);
    if (SafeAccess.isTextEmpty(valueString)) {
      return null;
    }
    return jsonDecode(valueString!);
  }
}
// 全局统一TextStyle
class FfcTextStyle extends TextStyle {
  FfcTextStyle({
    bool inherit = true,
    Color? color,
    double? fontSize,
    FontWeight? fontWeight,
    double? height,
    String? fontFamily,
    TextOverflow? overflow,
    String? package
  }) : super(
      inherit: inherit,
      color: color,
      fontSize: fontSize,
      fontWeight: fontWeight,
      height: height,
      fontFamilyFallback: Platform.isIOS ? ['PingFang SC','Apple Color Emoji'] : null,
      fontFamily: fontFamily ?? 'PingFang SC',
      overflow: overflow,
      package: package
  );
}