/// @Package lib.common
/// @File fuc_utils
/// <AUTHOR>
/// @Description 个人中心工具类
/// @Date 11-22-2023 周三 17:32

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:fbridge/fbridge.dart';
import 'package:fexp/fexp.dart';
import 'package:fliggy_flutter_community/components/ffc_dialog.dart';

import 'package:fliggy_router/fliggy_router.dart';
import 'package:flutter/material.dart';
import 'package:fbroadcast/fbroadcast.dart';
import 'package:fliggy_design/fliggy_design.dart';
import 'package:fliggy_flutter_community/model/ffc_home_model.dart';
import 'package:fliggy_flutter_community/common/ffc_data_utils.dart';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';
import 'package:fliggy_flutter_login/fliggy_login.dart';

import '../page/fliggy_play_map/utils/play_map_constant.dart';


// 常用工具类间接引入模块 全局引入
export 'package:fexp/fexp.dart';
export 'package:fliggy_design/fliggy_design.dart' show fd;
export 'package:flutter_common/flutter_common.dart';
export 'package:fround_image/fround_image.dart';
export 'package:fliggy_flutter_community/model/ffc_home_model.dart';
export 'package:ficonfont/ficonfont.dart';
export 'package:ficonfont/icons_mapping.dart';
export 'package:fbroadcast/fbroadcast.dart' hide StatefulBuilder;
export 'package:fliggy_router/fliggy_router.dart';
export 'package:fbridge/fbridge.dart' hide FliggyMtop;
export 'package:fliggy_flutter_community/network/ffc_network.dart';
export 'package:fliggy_flutter_community/components/des_cache_manager.dart';
export 'package:fliggy_flutter_community/common/ffc_data_utils.dart';
export 'package:fliggy_flutter_community/common/ffc_data_helper.dart';
export 'package:fliggy_flutter_community/components/ffc_expose_widget.dart';
export 'package:fliggy_mtop/fliggy_mtop.dart' hide ValueCallback;

class FfcUtils {
  //保存单例
  static final FfcUtils _singleton = FfcUtils._internal();

  //工厂构造函数
  factory FfcUtils() => _singleton;

  //私有构造函数
  FfcUtils._internal();
  late FexpPageTracker trackerHome; // 首页track
  // static late BuildContext context;
  // toast 默认展示两秒
  static void toast(String msg, {BuildContext? context}) {
    FBridgeApi.newInstance(context).toast(msg, 2000);
  }

  // 页面跳转
  static Future<Map> goPage(BuildContext context, bool isFlutter,
      {String? flutterPath, String? pageUrl, Map? params, Anim anim = Anim.slide}) async {
    if (isFlutter) {
      Map _flutterParams = {"un_flutter": true, "flutter_path": flutterPath};
      if (params != null && params.isNotEmpty) {
        _flutterParams.addAll(params);
      }
      return FliggyNavigatorApi.getInstance()
          .push(
        context,
        "page://flutter_view$flutterPath",
        params: _flutterParams,
        anim: anim,
      );
    } else {
      return FliggyNavigatorApi.getInstance().push(context, pageUrl ?? "", params: params ?? {}, anim: anim);
    }
  }

  // 回退
  static popPage(BuildContext context) {
    FliggyNavigatorApi.getInstance().pop(context);
  }
  // 统一桥
  static Future<T?> ffcBridge<T>(String method, {Map<String, dynamic>? arguments, BuildContext? context}) async{
    dynamic result;
    await FBridgeApi.newInstance(context)
        .invokeRaw<T>(method, arguments).match(
            (error) {
          result = error;
        },
            (data) {
          result = data;
        }
    ).run();
    return result;
  }
  // 是否登录
  static Future<bool> hasLogin() async {
    return await FliggyLoginApi.getInstance().hasLogin();
  }

  /// 拉起登录页 登录成功后调用刷新函数
  static void login() async {
    try {
      await FliggyLoginApi.getInstance().login(true).catchError((e) {
        print('login error: $e');
      });
    } catch (e) {
      print(e);
    }
  }

  static void trackCustom(BuildContext context, String eventName,
      {Map<String, String>? params}) {
    Map<String, String> allParams = {};
    if (params != null) {
      allParams.addAll(params);
    }

    FliggyUserTrackApi.getInstance().custom(
        context, eventName, PAGE_NAME, allParams);
  }

  // 点击埋点
  static void trackClickHome(
      BuildContext context,
      String spmCD,
      String controlName, {
        Map<String,String>? params
      }) {
    trackClickHomeSPM(context, controlName, "$SPM_AB$spmCD",customParams: params);
  }

  /// 玩法地图-点击埋点
  static void trackClickPlayMap(
      BuildContext context,
      String spmCD,
      String controlName, {
        Map<String,String>? params
      }) {
    trackClickHomeSPM(context, controlName, "${PlayMapConstant.spm_AB}$spmCD",customParams: params);
  }

  /// 点击埋点 全量SPM
  static void trackClickHomeSPM(
      BuildContext context,
      String controlName,
      String spm,{
        Map<String,String>? customParams
      }) {
    // 使用 首页track 上报点击事件
    FfcUtils().trackerHome.trackClick(context, spm, controlName, customParams);
  }

  /// 玩法地图-手动曝光埋点
  static void manualTrackExposurePlayMap(String spmCD, String trackName, Map<String?, dynamic>? trackArgs, {
    String? mixString,
    bool repeat = false,
    bool customArgs = false,
    Map<String, dynamic>? extArgs}) {
    // 上报手动曝光事件
    manualTrackExposure("${PlayMapConstant.spm_AB}$spmCD", trackName, trackArgs,
        mixString: mixString,
        repeat: repeat,
        customArgs: customArgs,
        extArgs: extArgs
    );
  }

  /// 手动曝光埋点 全量SPM
  static void manualTrackExposure(String? spm, String trackName, Map<String?, dynamic>? trackArgs, {
    String? mixString,
    bool repeat = false,
    bool customArgs = false,
    Map<String, dynamic>? extArgs}) {
    // 上报手动曝光事件
    FfcUtils().trackerHome.trackExposure(spm, trackName, trackArgs,
        mixString: mixString,
        repeat: repeat,
        customArgs: customArgs,
        extArgs: extArgs
    );
  }

  // 社区专用， 埋入自定义fpt
  static void trackFptCustom(
      BuildContext context,{
        Map<String, String>? params
      }) {
    FfcUtils().trackerHome.setCustomFpt(context, fptParams: params);
  }


  // 发送页面曝光事件
  static void onPageEnter(BuildContext context, FfcTab tab, {bool isFirst = false, bool onResume = false}) async {
    Map<String, dynamic>? args = tab.tabExt['data'];
    String spmCnt = SafeAccess.safeParseString(args?['spm-cnt']);
    String pageName = SafeAccess.safeParseString(args?['pageName']);
    Map<String, String>? extTrackArgs = getStringMap(args?['args']);
    Map<String, String>? extTrackFpt = getStringMap(args?['fpt']);
    //spm 是a.b.c.d 如果有a.b 自动补充a.b.0.0
    if (spmCnt.contains('.')) {
      List<String> spmList = spmCnt.split('.');
      if (spmList.length == 2) {
        spmCnt = "$spmCnt.0.0";
      }
    }
    if (spmCnt.isEmpty) {
      print("fromH5PageEnter spmCnt: error");
      return;
    }
    print("fromH5PageEnter spmCnt: $spmCnt, pageName: $pageName extTrackArgs: $extTrackArgs extTrackFpt: $extTrackFpt");
    FfcUtils().trackerHome.customPageEnter(
        context, spmCnt, pageName, args: extTrackArgs);
    if (isFirst) {
      if (Platform.isAndroid && !onResume) {
        // 上报埋点
        FfcUtils.ffcBridge('userTrack', arguments: {
          'action': 'tabEnter',
          'params': {"spm-cnt": spmCnt, "pageName": pageName},
        }, context: context);
      }
    } else {
      // 上报埋点
      FfcUtils.ffcBridge('userTrack', arguments: {
        'action': 'tabEnter',
        'params': {"spm-cnt": spmCnt, "pageName": pageName},
      }, context: context);
    }
    // 更新Fpt
    trackFptCustom(context, params: extTrackFpt);
  }
  // 补偿当前页面参数
  static Future<void> onPageUpdateTab(BuildContext context,FfcTab tab) async {
    Map<String, dynamic>? args = tab.tabExt['data'];
    Map<String, String>? extTrackArgs = getStringMap(args?['args']);
    // 更新页面属性
    if (!SafeAccess.isMapEmpty(extTrackArgs)) {
      await FliggyUserTrackApi.getInstance().updatePageProperties(
          context, extTrackArgs);
    }
  }
  // 更新页面参数
  static Future<void> onPageUpdate(BuildContext context, Map args) async {
    Map<String, String>? extTrackArgs = getStringMap(args);
    // 更新页面属性
    if (!SafeAccess.isMapEmpty(extTrackArgs)) {
      await FliggyUserTrackApi.getInstance().updatePageProperties(
          context, extTrackArgs);
    }
  }

  // 解析Url， 更新链接中 参数， 返回新的url
  static String updateUrlParams(String url, Map<String, String> params, {List<String> removeKeys = const []}) {
    if (url.isEmpty || params.isEmpty) return url;
    Uri uri = Uri.parse(url);
    Map<String, String> queryParams = Map<String, String>.from(uri.queryParameters);
    queryParams.addAll(params);
    queryParams.removeWhere((key, value) => removeKeys.contains(key));
    return uri.replace(queryParameters: queryParams).toString();
  }


  // 刷新社区发现
  static void refreshPostFresh(Map data) {
    if (!SafeAccess.isMapEmpty(data) && data.containsKey("contentId")) {
      String contentId = SafeAccess.safeParseString(data["contentId"]);
      if (SafeAccess.isTextEmpty(contentId)) return;
      changePostRefresh(FfcTab.COMMUNITY_FIND, customParams: {
        'contentId': contentId,
        if (data.containsKey('tabId')) 'tabId': data['tabId'],
      });
    }
  }

  static Map<String, String> getCardParams(int index, Map data,
      {Map<String, String> extTrackArgs = const {}, bool isQuestionCard = false}) {
    if (data.isEmpty) return {};

    Map<String, String> params = {
      "content_id": data['contentId'] ?? '',
      'type': data['contentType'] ?? '',
      'index': '$index'
    };

    bool hasImage = data.containsKey('imageList') && (data['imageList'] as List).isNotEmpty || data['contentType'] == 'video';
    if (!hasImage && !isQuestionCard) {
      params['type'] = 'text';
    }

    if (extTrackArgs.isNotEmpty) {
      params.addAll(extTrackArgs);
    }

    return params;
  }


  /// ------------- 通知处理 ----------------
  // 刷新通知
  static void changePostRefresh(FfcTab tab, {Map<String,dynamic>? customParams}) {
    FBroadcast.instance(tab.statusCode).broadcast(FfcDataUtils.FIND_REFRESH_KEY, value: customParams);
  }
  // 刷新通知监听
  static void changePostRefreshListener(BuildContext context, FfcTab tab, Function(Map<String,dynamic>?) callback) {
    FBroadcast.instance(tab.statusCode).register(FfcDataUtils.FIND_REFRESH_KEY, (value, _) {
      callback(value);
    }, context: context);
  }
  // 刷新通知监听 移除
  static void changePostRefreshUnListener(FfcTab tab, BuildContext context) {
    FBroadcast.instance(tab.statusCode).unregister(context);
  }

  // 页面状态
  // isPause true 暂停 false 恢复
  static void pageStatusChange(bool isPause) {
    FBroadcast.instance(PAGE_NAME).broadcast(FfcDataUtils.PAGE_STATUS_KEY, value: isPause);
  }
  // 页面状态监听
  static void pageStatusChangeListener(BuildContext context, Function(bool) callback) {
    FBroadcast.instance(PAGE_NAME).register(FfcDataUtils.PAGE_STATUS_KEY, (value, _) {
      callback(value);
    }, context: context);
  }
  // 页面状态监听 移除
  static void pageStatusChangeUnListener(BuildContext context) {
    FBroadcast.instance(PAGE_NAME).unregister(context);
  }
}

// 快捷解析服务端颜色数据
Color desColor(String? color, {String? defaultColor}) {
  if (color == null ||
      color.length != 7 ||
      int.tryParse(color.substring(1, 7), radix: 16) == null) {
    color = defaultColor ?? '#000000';
  }
  return Color(int.parse(color.substring(1, 7), radix: 16) + 0xFF000000);
}

// 判断链接Url是否正常并修复
String imgUrl(String url) {
  if (url.startsWith('http')) {
    return url;
  } else {
    // 默认返回一个透明图
    if (SafeAccess.isTextEmpty(url)) return "https://gw.alicdn.com/imgextra/i2/O1CN01B39Bga1XVX2K0PeCD_!!6000000002929-2-tps-1-1.png";
    return 'https:' + url;
  }
}

/// 显示退出确认弹窗
Future<void> showExitConfirm(BuildContext context,
    { String? title,
      String? confirmText,
      String? cancelText,
      bool isPop = false,
      bool isCancel = true,
      FDialogCallback? onTapCallback}) async {
  return showDialog(
    context: context,
    barrierDismissible: false,
    builder: (context) {
      return FfcDialog(
        title: title ?? '是否确认不保存当前修改',
        buttonsDirection: 'h',
        buttons: [
          if(isCancel)  FDialogButton(
            text: cancelText ?? '取消',
            color: fd.color_label,
            textColor: fd.color_midgray,
          ),
          FDialogButton(text: confirmText ?? '确认', primary: true)
        ],
        callback: (title, primary, payload) async {
          if (title == (confirmText ?? '确认') && isPop) {
            Navigator.pop(context);
          }
          if (onTapCallback != null) {
            onTapCallback(title, primary, payload);
          }
        },
      );
    },
  );
}

/// 为图片URL[originUrl]拼接CDN裁剪策略后缀的方法
/// [widthInLogicPixel]是ImageView的宽度，注意单位是逻辑像素. 该方法会根据视图的宽度找到适合它的CDN裁剪尺寸.
/// 如果这个参数不传，默认会返回320X320的图片.
/// 裁剪尺寸级别是可枚举的，目前支持：(90, 110, 200, 320, 460, 600, 760, 960, 1280),
/// CDN裁剪尺寸分级来源于图片库文档：{@https://yuque.antfin-inc.com/mobile_infra_network/aa40r8/tqpwpp}
String splicingCdnSuffix(String originUrl, {double widthInLogicPixel = 0.0}) {
  if (originUrl.isEmpty || !originUrl.contains('alicdn')) {
    return originUrl;
  }
  int width = 320;
  int height = 320;
  if (widthInLogicPixel > 0) {
    width = logicPixel2DevicePixel(widthInLogicPixel).toInt();
  }
  if (width <= 90) {
    width = 90;
  } else if (width <= 110) {
    width = 110;
  } else if (width <= 200) {
    width = 200;
  } else if (width <= 320) {
    width = 320;
  } else if (width <= 460) {
    width = 460;
  } else if (width <= 600) {
    width = 600;
  } else if (width <= 760) {
    width = 760;
  } else if (width <= 960) {
    width = 960;
  } else {
    width = 1280;
  }
  height = width;

// 判断有无http协议前缀，没有则补上
  if (originUrl.startsWith('http')) {
    return '${originUrl}_${width}x${height}q90.jpg';
  } else {
    return 'https:${originUrl}_${width}x${height}q90.jpg';
  }
}
double logicPixel2DevicePixel(double logicPixel) {
  return window.devicePixelRatio * logicPixel;
}

String simpleSplicingCdnSuffix(
    String originUrl,
    ) {
  if (originUrl.isEmpty || !originUrl.contains('alicdn')) {
    return originUrl;
  }

  if (originUrl.startsWith('http')) {
    return '${originUrl}_760x760q90.jpg';
  } else {
    return 'https:${originUrl}_760x760q90.jpg';
  }
}

String getJumpUrl(Map data) {
  try {
    return data['jumpInfo']['jumpH5Url'];
  } catch (e) {
    return '';
  }
}

Map<String, String> getStringMap(Map? map) {
  Map<String, String> result = {};
  if (map == null) {
    return result;
  }
  map.forEach((key, value) {
    result[key.toString()] = value.toString();
  });
  return result;
}

