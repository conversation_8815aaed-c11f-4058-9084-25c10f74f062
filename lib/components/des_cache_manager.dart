import 'package:fliggy_flutter_community/common/ffc_utils.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as p;
import 'package:file/local.dart';
import 'package:path_provider/path_provider.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_cache_manager/src/storage/file_system/file_system_io.dart';
import 'package:cached_network_image/cached_network_image.dart';
/// <AUTHOR>
/// @date Created on 2024/5/11
/// @company Alibaba Group
/// @description 图片缓存管理类

class DesCacheManager {
  static const key = 'DesCacheKey';
  // 缓存管理
  static CacheManager instance = DesImageCacheManager();

  static Future<double> cacheSize() async {
    var baseDir = await getTemporaryDirectory();
    var path = p.join(baseDir.path, key);

    var fs = const LocalFileSystem();
    var directory = fs.directory((path));
    return (await directory.stat()).size / 8 / 1000;
  }

  static Future<void> clearCache() async {
    var baseDir = await getTemporaryDirectory();
    var path = p.join(baseDir.path, key);

    var fs = const LocalFileSystem();
    var directory = fs.directory((path));
    await directory.delete(recursive: true);
  }
}

class DesImageCacheManager extends CacheManager with ImageCacheManager {
  static const key = 'DesCacheKey';

  DesImageCacheManager() : super(Config(
    key,
    stalePeriod: const Duration(days: 2), // 存储2天最大
    maxNrOfCacheObjects: 100,
    repo: JsonCacheInfoRepository(databaseName: key),
    fileSystem: IOFileSystem(key),
    fileService: HttpFileService(),
  ));
}

// 自定义网络图片
// 渲染图切换 FRoundImage
// Widget DesCacheNetWorkImage(String url, {
//   Key? key,
//   BoxFit? fit,
//   double? width,
//   double? height,
//   PlaceholderWidgetBuilder? placeholder,
//   Duration fadeInDuration = const Duration(milliseconds: 250),
//   Duration placeholderFadeInDuration = Duration.zero,
//   Curve fadeOutCurve = Curves.easeOut,
//   Duration fadeOutDuration = const Duration(milliseconds: 250),
//   bool useOldImageOnUrlChange = true,
//   Alignment alignment = Alignment.center,
// }) {
//  return FRoundImage.network(imgUrl(url),width: width, height: height, fit: fit,);
// }
class FfcCacheNetWorkImage extends CachedNetworkImage {
  FfcCacheNetWorkImage(String url, {
    Key? key,
    BoxFit? fit,
    double? width,
    double? height,
    PlaceholderWidgetBuilder? placeholder,
    Duration fadeInDuration = const Duration(milliseconds: 250),
    Duration placeholderFadeInDuration = Duration.zero,
    Curve fadeOutCurve = Curves.easeOut,
    Duration fadeOutDuration = const Duration(milliseconds: 250),
    bool useOldImageOnUrlChange = true,
    Alignment alignment = Alignment.center,
    ImageWidgetBuilder? imageBuilder
  }) : super(
      key: key,
      cacheKey: imgUrl(url),
      imageUrl: imgUrl(url),
      width: width,
      height: height,
      placeholder: placeholder,
      cacheManager: DesCacheManager.instance,
      fit: fit ?? BoxFit.cover,
      fadeInDuration: fadeInDuration,
      fadeOutCurve: fadeOutCurve,
      fadeOutDuration: fadeOutDuration,
      placeholderFadeInDuration: placeholderFadeInDuration,
      alignment: alignment,
      imageBuilder: imageBuilder,
      useOldImageOnUrlChange: useOldImageOnUrlChange // url 改变时使用上次的图像
  );
}

// 自定义网络图片
class FfcCacheNetWorkImageProvider extends CachedNetworkImageProvider {
  FfcCacheNetWorkImageProvider(String url, {
    int? maxWidth,
    int? maxHeight
  }) : super(
      imgUrl(url),
      cacheKey: imgUrl(url),
      cacheManager: DesCacheManager.instance,
      maxWidth: maxWidth,
      maxHeight: maxHeight
  );
}