import 'package:fliggy_flutter_community/common/ffc_data_helper.dart';
import 'package:fliggy_flutter_community/page/components/comment/comment_input_page.dart';
import 'package:fliggy_flutter_community/page/components/image_preview/image_preview_page.dart';
import 'package:fliggy_flutter_community/page/components/publish/community_panel.dart';
import 'package:fliggy_flutter_community/page/ffc_home_page.dart';
import 'package:fliggy_flutter_community/page/ffc_middle_diversion_page.dart';
import 'package:fliggy_flutter_community/page/ffc_play_map_page.dart';
import 'package:flutter_common/flutter_common.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:fliggy_font/fliggy_font.dart';

/// 【非常重要】！！！
/// 使用图片，必须要加上该 package 参数，否则在动态化中将无法加载到图片
/// 如：Image.asset("assets/images/bg.png", package: package,),
String package = 'fliggy_flutter_community';

/// 动态包版本
String bundleVersion = '0.0.0';


/// 页面在此处配置<br>
/// 页面名称规范：<业务>/<页面>，如 /fhotel/media_list
/// <br>必须遵从规范，否则无法使用 aion 离线包，以及进行内部页面跳转
///
/// 【禁止！！！】禁止通过变量的方式向 pages 中添加页面，否则页面无法找到！！！
/// 【禁止！！！】禁止通过变量的方式向 pages 中添加页面，否则页面无法找到！！！
/// 【禁止！！！】禁止通过变量的方式向 pages 中添加页面，否则页面无法找到！！！
Map<String, FliggyPageBuilder> pages = {
  "/fliggy_flutter_community/home": (pageName, params, _) => FfcMiddleDiversionPage(params: params ?? {}),

  "/fliggy_flutter_community/city_map": (pageName, params, _) =>  FfcPlayMapPage(params: params ?? {}),

  "/fliggy_flutter_community/destination_panel": (pageName, params, _) => FfcCommunityPanel( params: params??{}),
  "/fliggy_flutter_community/comment_input_page" : (pageName, params, _) =>
      FfcCommentInputPage(params??{}),
  "/fliggy_flutter_community/image_preview_page" : (pageName, params, _) =>
      FfcImagePreviewPage(params??{}),
  "/fliggy_flutter_community/page": (pageName, params, _) => FfcHomePage(title: "飞猪社区", params: params??{}),
};

/// 由于 AionBundle 中，所有的自定义对象都是全新的独立对象（FlutterBoost 除外），因此宿主工程中的变量及状态与 AionBundle 中的都是不同的。<br>
/// Aion 编译出来的 Bundle 产物，所有的自定义类（即非 Flutter SDK 中的类）都会增加 _kbc 后缀变成新的类，因此即使是同一段代码，也与宿主工程完全不同了。<br>
/// 开发者应该根据需要，进行自己的初始化。<br>
/// 如在需要 [DXEngine] 的 Bundle 中，开发者应该在此处进行 [DXEngine] 的初始化。<br>
/// 【⚠️注意】：多余的初始化会引入无意义的代码到产物中，从而导致产物体积增加
void init() {
  FfcDataHelper().getUserEnv();
  FliggyFont('fliggy_flutter_community').load();
}

class FliggyFlutterCommunityModule with FliggyModule {
  FliggyFlutterCommunityModule() {
    biz = package;
    pageNames = pages.keys.toList();
  }

  @override
  void onPageRegister() {
    FliggyNavigatorApi.getInstance().registerPageBuilders(pages);
  }

  @override
  void onModuleInit() {
    FfcDataHelper().getUserEnv();
    FliggyFont('fliggy_flutter_community').load();
  }
}
