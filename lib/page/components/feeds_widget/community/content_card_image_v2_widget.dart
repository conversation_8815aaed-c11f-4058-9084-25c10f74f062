

import 'dart:async';

import 'package:fliggy_flutter_community/common/ffc_utils.dart';
import 'package:flutter/material.dart';

/// <AUTHOR>
/// @date Created on 2024/7/13
/// @email <EMAIL>
/// @company Alibaba Group
/// @description 内容卡片图片

class ContentCardImageV2Widget extends StatelessWidget {
  static const double IMAGE_FACTOR = 3;
  static const double CORNER = 6;

  final int index;
  final Map data;
  final String spmC;
  final Map<String, String> extTrackArgs;

  ContentCardImageV2Widget(this.index, this.data, this.spmC,
      {this.extTrackArgs = const {}});

  @override
  Widget build(BuildContext context) {
    String contentType = SafeAccess.safeParseString(data["contentType"]);

    double width = SafeAccess.safeParseDouble(data['coverWidth'], def: 240);
    double height = SafeAccess.safeParseDouble(data['coverWidth'], def: 240);
    double factor = width / height;

    if (contentType == "video") {
      String imageUrl = SafeAccess.safeParseString(data["coverUrl"]);
      return _buildVideoCover(factor, imageUrl);
    }
    List image = SafeAccess.safeParseList(data["imageList"]);
    if (image.length == 1) {
      return GestureDetector(
        onTap: () {
          FfcUtils.trackClickHome(context, "$spmC.photo", "photo",
              params: FfcUtils.getCardParams(this.index, data,
                  extTrackArgs: extTrackArgs));
          FliggyNavigatorApi.getInstance().push(context,
              "page://flutter_view/fliggy_flutter_community/image_preview_page",
              anim: Anim.none,
              params: {
                'photos': [{'url': SafeAccess.safeGet(list: image, index: 0, defaultValue: '')}],
                'initIndex': 0,
              });
        },
        child: _buildOneImage(simpleSplicingCdnSuffix(image[0] ?? '')),
      );
    }

    if (image.length == 2) {
      return _buildTwoImage(context, image);
    }

    return Container(
      margin: EdgeInsets.only(top: 9),
      child: Stack(
        children: [
          Row(
            children: [
              _buildImageItem(
                  context,
                  0,
                  229,
                  159,
                  BorderRadius.only(
                      topLeft: Radius.circular(CORNER),
                      bottomLeft: Radius.circular(CORNER)),
                  image[0]),
              SizedBox(width: 3),
              Column(
                children: [
                  _buildImageItem(
                      context,
                      1,
                      113,
                      78,
                      BorderRadius.only(topRight: Radius.circular(CORNER)),
                      image[1]),
                  SizedBox(height: 3),
                  _buildImageItem(
                      context,
                      2,
                      113,
                      78,
                      BorderRadius.only(bottomRight: Radius.circular(CORNER)),
                      image[2])
                ],
              )
            ],
          ),
          if (image.length > 3) Positioned(
              bottom: 6,
              right: 6,
              child: Container(
                width: 23,
                height: 18,
                decoration: BoxDecoration(
                    borderRadius:
                    BorderRadius.all(Radius.circular(3)),
                    color: fd.color_darkgray.withOpacity(0.6)),
                child: Center(
                  child: Text(
                    "${image.length}",
                    style: FfcTextStyle(
                        color: fd.color_white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600),
                  ),
                ),
              ))
        ],
      ),
    );
  }

  Widget _buildVideoCover(double factor, String url) {
    double height = 240 * factor;
    if (height > 240) {
      height = 240;
    } else if (height < 132) {
      height = 132;
    }
    return Stack(
      children: [
        Container(
          width: 240,
          margin: EdgeInsets.only(top: 9),
          constraints: BoxConstraints(minHeight: 138, maxHeight: 240),
          decoration: BoxDecoration(
            color: desColor("#D2D4D9"),
            borderRadius: BorderRadius.circular(CORNER),
          ),
          clipBehavior: Clip.hardEdge,
          child: ClipRect(
            child: FfcCacheNetWorkImage(
              simpleSplicingCdnSuffix(url),
              width: 240 * IMAGE_FACTOR,
              height: 240 * IMAGE_FACTOR,
              fit: BoxFit.cover,
              alignment: Alignment.center,
            ),
          ),
        ),
        Positioned.fill(
          child: Container(
            color: Colors.black.withOpacity(0.05),
          ),
        ),
        Positioned(
          left: (240 - 35) / 2,
          top: (height - 35) / 2,
          child: Image.network(
              "https://gw.alicdn.com/imgextra/i1/O1CN01QFK5X41xpO4yQHBsY_!!6000000006492-2-tps-64-60.png",
              width: 35,
              height: 35),
        )
      ],
    );
  }

  Widget _buildTwoImage(BuildContext context, List image) {
    return Container(
      margin: EdgeInsets.only(top: 9),
      child: Row(
        children: [
          _buildImageItem(
              context,
              0,
              118,
              118,
              BorderRadius.only(
                  topLeft: Radius.circular(CORNER),
                  bottomLeft: Radius.circular(CORNER)),
              image[0]),
          SizedBox(width: 3),
          _buildImageItem(
              context,
              1,
              118,
              118,
              BorderRadius.only(
                  topRight: Radius.circular(CORNER),
                  bottomRight: Radius.circular(CORNER)),
              image[1]),
        ],
      ),
    );
  }

  Widget _buildImageItem(BuildContext context, int index, double width,
      double height, BorderRadiusGeometry? borderRadius, String image) {
    String cdnUrl = simpleSplicingCdnSuffix(image);
    return GestureDetector(
      onTap: () {
        FfcUtils.trackClickHome(context, "$spmC.photo", "photo",
            params: FfcUtils.getCardParams(this.index, data,
                extTrackArgs: extTrackArgs));
        FliggyNavigatorApi.getInstance().push(context,
            "page://flutter_view/fliggy_flutter_community/image_preview_page",
            anim: Anim.fade,
            params: {
              'photos': data['imageList']?.map((url) => {"url": url}).toList(),
              'initIndex': index,
            });
      },
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: borderRadius,
          color: desColor("#D2D4D9"),
        ),
        clipBehavior: Clip.hardEdge,
        child: Stack(
          children: [
            FfcCacheNetWorkImage(
              cdnUrl,
              width: width * IMAGE_FACTOR,
              height: height * IMAGE_FACTOR,
              fit: BoxFit.cover,
            ),
            Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.05),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOneImage(String imageUrl) {
    bool longImage = false;
    return Container(
      width: 240,
      margin: EdgeInsets.only(top: 9),
      constraints: BoxConstraints(minHeight: 138, maxHeight: 240),
      decoration: BoxDecoration(
        color: desColor("#D2D4D9"),
        borderRadius: BorderRadius.circular(6),
      ),
      clipBehavior: Clip.hardEdge,
      child: StatefulBuilder(
          builder: (context, setState) {
            return Stack(
              children: [
                ClipRect(
                  child: FfcCacheNetWorkImage(
                    imageUrl,
                    width: 240,
                    fit: BoxFit.fitWidth,
                    alignment: Alignment.topCenter,
                    imageBuilder: (context, imageProvider){
                      isLongImage(imageProvider).then((value) {
                        // 刷新展示长图标识
                        if(value == true) {
                          setState(() {
                            longImage = value;
                          });
                        }
                      });
                      return Image(image: imageProvider, fit: BoxFit.fitWidth,alignment: Alignment.topCenter,);
                    },
                  ),
                ),
                Positioned.fill(
                  child: Container(
                    color: Colors.black.withOpacity(0.05),
                  ),
                ),
                if (longImage)
                  Positioned(
                      bottom: 6,
                      right: 6,
                      child: Container(
                        width: 34,
                        height: 18,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.all(Radius.circular(3)),
                            color: fd.color_darkgray.withOpacity(0.6)),
                        child: Center(
                          child: Text(
                            "长图",
                            style: FfcTextStyle(
                                color: fd.color_white,
                                fontSize: 12,
                                fontWeight: FontWeight.w600),
                          ),
                        ),
                      ))
              ],
            );
          }
      ),
    );
  }

  // 获取是否长图
  Future<bool> isLongImage(ImageProvider img) async {
    Completer completer = Completer<ImageInfo>();
    img.resolve(const ImageConfiguration()).addListener(
        ImageStreamListener((info, _) => completer.complete(info)));
    ImageInfo imageInfo = await (completer.future as FutureOr<ImageInfo>);
    return (imageInfo.image.height / imageInfo.image.width) > 3.0;
  }
}
