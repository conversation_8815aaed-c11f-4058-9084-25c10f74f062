

import 'package:fliggy_flutter_community/common/ffc_utils.dart';
import 'package:flutter/material.dart';

class FeedsImageItem extends StatelessWidget {
  final Map data;
  final String tabName;
  final int index;
  final String type;
  FeedsImageItem(this.data, this.tabName, this.index, this.type);
  @override
  Widget build(BuildContext context) {
    String imagesPath =  imgUrl(data['imagesPath']);

    if (imagesPath == '') {
      return Container();
    }

    String spmCD = 'feeds.item_' + index.toString();
    String controllerName = 'feeds_item_' + index.toString();
    return FfcExposeWidget(
        spmCD: spmCD,
        controllerName: controllerName,
        params: {
          'tab' : tabName,
          'type' : type
        },
        child: GestureDetector(
          onTap: () {
            FfcUtils.trackClickHome(context, spmCD, controllerName, params: {
              'tab' : tabName,
              'type' : type
            });

            String? jumpUrl = data['jumpInfo']['jumpH5Url'];
            if (jumpUrl != null) {
              FliggyNavigatorApi.getInstance().push(context, jumpUrl);
            }
          },
          child: imagesPath != '' ? Container(
            width: 174,
            height: 275,
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(borderRadius: BorderRadius.circular(6)),
            child: FfcCacheNetWorkImage(imagesPath,
              fit: BoxFit.cover,
              height: 275,
              width: 174,
              fadeInDuration: Duration(milliseconds: 0),
              fadeOutDuration: Duration(milliseconds: 0),
            ),
          ) : Container(),
        ),
    );
  }
}