import 'dart:io';
import 'package:visibility_detector/visibility_detector.dart';
import 'package:fliggy_flutter_community/common/ffc_utils.dart';
import 'package:flutter/material.dart';
import 'package:fliggy_webview/fliggy_webview.dart';
import 'package:flutter/services.dart';

/// @Package  
/// @File ffc_h5_container
/// <AUTHOR>
/// @Description H5容器
/// @Date 10-14-2024 周一 16:51


class FfcH5Container extends StatefulWidget {
  final String url;
  final int timeInterval;
  final FfcTab tab;
  _FfcH5ContainerState _state = _FfcH5ContainerState();
  FfcH5Container({Key? key,
    required this.url,
    required this.tab,
    this.timeInterval = 0
  }) : super(key: key);

  // 强制执行刷新
  void refreshUrl({String? refreshUrl}) {
    if (SafeAccess.isTextEmpty(refreshUrl) ) {
      return;
    }
    if (_state._url != refreshUrl) {
      _state._url = refreshUrl!;
      debugPrint("FfcH5Container refreshUrl:$refreshUrl hash:${_state.hashCode}");
      if (_state.webViewController.isInitialized()) {
        _state.webViewController.reloadUrl(refreshUrl);
      }
    }
  }
  void refreshTimeInterval({int? refreshTimeInterval}) {
    if (refreshTimeInterval == null) {
      return;
    }
    if (_state._timeInterval != refreshTimeInterval) {
      _state._timeInterval = refreshTimeInterval;
      debugPrint("FfcH5Container refreshTimeInterval:$refreshTimeInterval hash:${_state.hashCode}");
      if (_state.webViewController.isInitialized()) {
        _state.webViewController.evalJavascript('window.refreshTrip()');
      }
    }
  }
  // ios 18.2 问题修复 reload
  void reloadUrl({String? refreshUrl}) {
    if (SafeAccess.isTextEmpty(refreshUrl) ) {
      return;
    }
    _state._url = refreshUrl!;
  }

  @override
  State<FfcH5Container> createState() => _state;
}

class _FfcH5ContainerState extends State<FfcH5Container> with AutomaticKeepAliveClientMixin {
  FliggyWebViewController? _webViewController;
  String _url = "";
  int _timeInterval = 0;
  @override
  void initState() {
    super.initState();
    _url = widget.url;
    _timeInterval = widget.timeInterval;
    debugPrint("FfcH5Container initState hash:${this.hashCode}");
  }

  @override
  void didUpdateWidget(covariant FfcH5Container oldWidget) {
    if (oldWidget.url != widget.url) {
      if (webViewController.isInitialized()) {
        webViewController.reloadUrl(widget.url);
      }
      _url = widget.url;
    }
    if (oldWidget.timeInterval != widget.timeInterval) {
      if (webViewController.isInitialized()) {
        webViewController.evalJavascript('window.refreshTrip()');
      }
      _timeInterval = widget.timeInterval;
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    debugPrint("FfcH5Container build hash:${this.hashCode}");
    super.build(context);
    return VisibilityDetector(
      key: Key(widget.tab.statusCode),
      onVisibilityChanged: (info) {
        if (info.visibleFraction >= 1) {
          debugPrint('${widget.runtimeType}社区页可见度: ${info.visibleFraction}');
          // 页面完全可见
          // FfcUtils.onPageEnter(context, widget.tab);
        } else if (info.visibleFraction <= 0){
          debugPrint('${widget.runtimeType}社区页可见度: ${info.visibleFraction}');
          // 页面完全不可见
          // onPause();
        }
      },
      child: SafeArea(
        top: false,
        left: false,
        right: false,
        bottom: Platform.operatingSystem == "ohos" ? false : true,
        child: Padding(
          padding: EdgeInsets.only(bottom: Platform.isIOS ? 62.0 : 0.0),
          child: FliggyWebViewWidget(
            url: widget.url,
            renderType: PlatformViewRenderType.hybridComposition,
            webViewController: webViewController,
          ),
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;

  FliggyWebViewController get webViewController {
    if (_webViewController == null) {
      _webViewController = FliggyWebViewController();
    }
    return _webViewController!;
  }
}