import 'dart:io';

import 'package:fliggy_flutter_community/common/ffc_utils.dart';
import 'package:fliggy_flutter_community/page/components/image_preview/image_preview_item.dart';
import 'package:flutter/material.dart';


/// <AUTHOR>
/// @date Created on 2024/1/4
/// @email <EMAIL>
/// @company Alibaba Group
/// @description 图片预览黑灯页

class ImagePreviewOverlay extends StatefulWidget {
  final List<String> imageList;
  final VoidCallback onTapClose;
  final int initIndex;

  ImagePreviewOverlay(this.imageList, this.onTapClose, this.initIndex);

  @override
  State<StatefulWidget> createState() => _ImagePreviewOverlayState();
}

class _ImagePreviewOverlayState extends State<ImagePreviewOverlay> {
  int _currentIndex = 0;
  late PageController _controller;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initIndex;
    _controller = PageController(initialPage: _currentIndex);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () => widget.onTapClose(),
        child: Material(
          color: Colors.black,
          child: Stack(
            children: <Widget>[
              // 中间Banner
              _buildBanner(),
              // 媒体索引Indicator
              _buildMediaIndex(_currentIndex + 1, widget.imageList.length),
            ],
          ),
        ));
  }

  Widget _buildMediaIndex(int i, int length) {
    return Positioned(
      bottom:
          Platform.isAndroid ? 8 : (60 + MediaQuery.of(context).padding.bottom),
      left: 15,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 6, vertical: 5),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          '$i/$length',
          style: FfcTextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  Widget _buildBanner() {
    return PageView.builder(
      controller: _controller,
      itemCount: widget.imageList.length,
      itemBuilder: (BuildContext context, int index) {
        String imageUrl = widget.imageList[index];
        if (imageUrl.isEmpty) {
          return Container();
        }

        String cdnUrl = splicingCdnSuffix(imageUrl, widthInLogicPixel: 300);

        return Center(
          child: ImagePreviewItem(cdnUrl),
        );
      },
      onPageChanged: (int index) {
        safeSetState(() {
          _currentIndex = index;
        });
      },
    );
  }
}
