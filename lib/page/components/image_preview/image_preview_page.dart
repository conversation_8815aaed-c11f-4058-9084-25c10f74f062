

import 'dart:convert';

import 'package:fliggy_flutter_community/common/ffc_utils.dart';
import 'package:fliggy_flutter_community/page/components/image_preview/image_preview_overlay.dart';
import 'package:flutter/material.dart';

/// <AUTHOR>
/// @date Created on 2024/5/7
/// @email <EMAIL>
/// @company Alibaba Group
/// @description 社区图片预览黑灯页

class FfcImagePreviewPage extends StatefulWidget {
  final Map params;

  FfcImagePreviewPage(this.params);

  @override
  State<StatefulWidget> createState() => _FfcImagePreviewPageState(params);
}

class _FfcImagePreviewPageState extends State<FfcImagePreviewPage> {
  /// 页面入参
  Map _params;

  _FfcImagePreviewPageState(this._params);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    List photos = [];
    if (_params['photos'] is String) {
      photos = _parseJSONString2Map(_params['photos']);
    } else {
      photos = _params['photos'];
    }
    int initIndex = SafeAccess.safeParseInt(_params['initIndex']);
    List<String> imageList = photos.map((photo) {
      return photo['url'].toString();
    }).toList();

    // 默认cdn裁剪，可关闭
    bool needCdnCrop = true;
    if(_params['needCdnCrop'] is bool){
      needCdnCrop = _params['needCdnCrop'];
    }

    return ImagePreviewOverlay(imageList, () {
      Navigator.of(context).pop();
    }, initIndex, needCdnCrop: needCdnCrop);
  }

  // 将json字符串转换为Map
  List<Map<String, String>> _parseJSONString2Map(String str) {
    List<Map<String, String>> result = [];
    if (str.isEmpty) {
      return result;
    }
    List<dynamic> list = jsonDecode(str);
    list.forEach((element) {
      if (element is Map) {
        result.add(Map<String, String>.from(getStringMap(element)));
      }
    });
    return result;
  }
}