import 'dart:convert';
import 'dart:io';

import 'package:fliggy_flutter_community/common/ffc_utils.dart';
import 'package:fliggy_flutter_community/components/ffc_custom_indicator.dart';
import 'package:fliggy_flutter_community/components/ffc_custom_tabbar.dart';
import 'package:fliggy_flutter_community/page/components/h5_container/ffc_h5_container.dart';
import 'package:fliggy_flutter_community/page/components/header/ffc_title_bar_more.dart';
import 'package:fliggy_flutter_community/page/components/publish/des_publish.dart';

import 'package:fliggy_flutter_login/fliggy_login.dart';
import 'package:fliggy_flutter_community/page/fliggy_find/community_find_page.dart';
import 'package:fliggy_flutter_community/page/fliggy_follow/community_follow_page.dart';
import 'package:flutter/material.dart';

import 'ffc_home_page_logic.dart';


/// @Package
/// @File ffc_home_page
/// <AUTHOR>
/// @Description 社区Tab首页框架
/// @Date 08-30-2024 周五 10:44


class FfcHomePage extends StatefulWidget {
  final String title;
  final Map? params;
  const FfcHomePage({Key? key,
    required this.title,
    this.params

  }) : super(key: key);

  @override
  State<FfcHomePage> createState() => FfcHomePageState();
}

class FfcHomePageState extends State<FfcHomePage> with FfcHomePageLogic, FexTrackScore {

  TabController? tabController;
  /// tab 高度
  final double tabBarHeight = 40;
  /// 默认选中看世界Tab
  int defaultIndex = 0;
  /// 当前选中Tab
  int currentIndex = 0;
  ValueNotifier<TabBarModel> currentTab = ValueNotifier<TabBarModel>(TabBarModel());
  ValueNotifier<TabColor> tabStyle = ValueNotifier<TabColor>(TabColor());
  ValueNotifier<TabBarModel> onDoubleTap = ValueNotifier<TabBarModel>(TabBarModel());
  /// 用户信息数据
  Map? _userInfo = {};
  /// 搜索栏数据
  Map? _searchBar = {};
  DestinationModel destinationModel = DestinationModel();
  bool isTab = true;
  double offsetH5 = 0.0;

  bool isFirst = true;
  // 动态Tab数据
  List<TabData> tabsData = [];
  // 发现子Tab 数据
  List subTabData = [];
  // Tab 原始数据
  List tabInfo = [];
  // 是否透传Tab信息
  bool isTabLoad = false;

  // 发现刷新控制器
  int timeInterval = 0;

  bool isProcessing = false;

  // 上报页面留存
  int viewTime = 0;
  // 发布器默认数据
  Map _publishParams = {
    "publishButtonImage": "https://gw.alicdn.com/imgextra/i3/O1CN01vJsg6J1J2lYmJaVnC_!!6000000000971-54-tps-165-165.apng",
    "publishPageUrl": "page://common_publish_tp"
  };

  FfcH5Container? _h5ContainerLookWord;
  FfcH5Container? _h5ContainerCity;

  late final FBridge _bridge;
  @override
  void initState() {
    // 执行异步任务
    FfcDataHelper().getUserEnv();
    super.initState();
    // 生命周期与埋点
    FfcUtils().trackerHome = tracker;
    _bridge = FBridgeApi.newInstance(context);
    _bridge.registerEvent("WV.Resume", (e) {
      onResume(e);
    });
    _bridge.registerEvent("WV.Pause", (e) {
      onPause(e);
    });
    //WV.Resume会早于initState发送，因此这里用ready桥做双向通讯
    _bridge.callSafe('ready', {});

    // 初始化页面数据
    initData();
    // 预请求
    _preRequest();
    // 注册监听
    _registerNotifier();
    // 请求Tab 数据
    _requestTabData();
    // 请求用户信息
    _requestUserInfo();
    // 补偿第一次页面enter
    FfcUtils.onPageEnter(state.context, state.currentTab.value.currentTab, isFirst: true);
  }

  void _preRequest() {
    FfcUtils.ffcBridge('cacheFetch',context: context, arguments: {
      'url': FfcDataHelper().getDestinationUrl(destinationModel.destId),
    });

    FfcUtils.ffcBridge('cacheFetch',context: context, arguments: {
      'url': FfcDataHelper().getWorldViewUrl(),
      'rule':{
        'url': 'outfliggys.(wapa|m).taobao.com/app/trip/rx-dest-trip-world/pages/home',
        'keyArgs':['from'],
        'maxAge': 7*24*60*60,
        'cachePolicy': 4,
        'type':'ssr'
      }
    });

    FfcUtils.ffcBridge('cacheFetch',context: context, arguments: {
      'url': FfcDataHelper().getCitySelectUrl,
      'rule':{
        'url': 'market.(wapa|m).taobao.com/app/trip/rx-dest-city-choose/pages/home',
        'keyArgs':['from'],
        'maxAge': 7*24*60*60,
        'cachePolicy': 4,
        'type':'ssr'
      }
    });
    // &fpt=ftid(017803)
  }

  // 请求Tab 数据
  // isRefresh 是否强制刷新
  void _requestTabData({bool isRefresh = false}) {
    if (isTabLoad && !isRefresh) { // 当不是透传的Tab时才会主动请求接口
      return;
    }
    TitleBarNet.requestCommunityTab(context).then((value) {
      if (value.success) {
        tabInfo = SafeAccess.safeParseList(value.data["data"]);
        if (SafeAccess.isListEmpty(tabInfo)) return;
        // 缓存Tab
        FfcDataHelper.setStorageAssemble<List>(FfcDataUtils.DESTINATION_TAB_CACHE_KEY , tabInfo);
        // 更新Tab
        updateTabData(convertTab(tabInfo));
      }
    }).catchError((error){
      return new MtopResponseModel();
    });
  }

  // 请求用户信息数据
  void _requestUserInfo() {
    TitleBarNet.requestUserInfo(context).then((value) {
      if (value.success) {
        // 先剔除接口中返回的tabInfo
        Map result = value.data['data'] ?? {};
        // 刷新发布器数据
        if (SafeAccess.safeParseMap(result['publishInfo']).isNotEmpty) {
          _publishParams = SafeAccess.safeParseMap(result['publishInfo']);
        }
        // 刷新用户头像数据，仅社区有效
        safeSetState(() {
          _userInfo = SafeAccess.safeParseMap(result['userInfo']);
          _searchBar = SafeAccess.safeParseMap(result['searchBar']);
        });
      }
    }).catchError((error) {
      return new MtopResponseModel();
    });
  }

  void _registerNotifier() {
    // 监听H5侧通知
    FBroadcast.instance('native').register("fromH5Control", (value, callback) {
      debugPrint("Tab fromH5Control value: $value");
      if (value != null && value is Map) {
        offsetH5 = SafeAccess.safeParseDouble(value["offset"], def: 0.0);
        if (offsetH5 > 0.5 &&
            currentTab.value.currentTab == FfcTab.CITY &&
            currentTab.value.titleBarColor != fd.color_darkgray) {
          FfcTab.CITY.updateTabExt({
            "tabColor": fd.color_darkgray,
            "canSetColor": true,
          });
          currentTab.value = currentTab.value.copyWith(titleBarColor: fd.color_darkgray);
          tabStyle.value = tabStyle.value.copyWith(labelColor: fd.color_darkgray, unselectedLabelColor: fd.color_darkgray, indicatorColor: fd.color_darkgray);
          updateStatusBarBrightness('dark');
        } else if (offsetH5 < 0.5 &&
            currentTab.value.currentTab == FfcTab.CITY &&
            currentTab.value.titleBarColor != fd.color_white) {
          FfcTab.CITY.updateTabExt({
            "tabColor": fd.color_white,
            "canSetColor": true,
          });
          currentTab.value = currentTab.value.copyWith(titleBarColor: fd.color_white);
          tabStyle.value = tabStyle.value.copyWith(labelColor: fd.color_white, unselectedLabelColor: fd.color_white, indicatorColor: fd.color_white);
          updateStatusBarBrightness('light');
        }
      }
    }, context: context);

    FBroadcast.instance('native').register("fromH5PageEnter", (value, callback) {
      print("Tab fromH5PageEnter value: $value");
      if (value != null && value is Map<String,dynamic>) {
        // 过滤 value 中的fpt，防止覆盖掉fpt数据
        if (value.containsKey('fpt')) {
          value.remove('fpt');
        }
        if (value.containsKey('args') && value['args'] is Map) {
          value['args'].remove('fpt');
        }
        String? spm = SafeAccess.safeParseString(value['spm-cnt']);
        if (spm.contains("181.9406239")) { // CommunityFind
          // FfcTab.COMMUNITY_FIND.updateTabExt({
          //   "data": value
          // });
          // FfcUtils.onPageUpdate(context, value);
        }
        if (spm.contains("181.29645465")) { // LookWorld
          FfcTab.LOOK_WORLD.updateTabExt({
            "data": value
          });
          FfcUtils.onPageUpdate(context, value['args'] ?? {});
        }
        if (spm.contains("181.29334294")) { // City
          FfcTab.CITY.updateTabExt({
            "data": value
          });
          FfcUtils.onPageUpdate(context, value['args'] ?? {});
        }
      }
    }, context: context);
    // 监听登录
    FliggyLoginApi.getInstance().registerLoginReceiver(_loginChange);
  }
  void _unRegisterNotifier() {
    FBroadcast.instance('native').unregister(context);
    FliggyLoginApi.getInstance().unregisterLoginReceiver(_loginChange);
  }

  void _loginChange(LoginNotify type, int requestCode) {
    // _requestTabData(isRefresh: true); // 强制刷新Tab接口
    _requestUserInfo(); // 强制刷新用户信息
    currentTab.value = currentTab.value.copyWith(timestamp: DateTime.now().millisecondsSinceEpoch);
  }

  @override
  void didUpdateWidget(covariant FfcHomePage oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _unRegisterNotifier();
    _bridge.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        // backgroundColor: fd.color_white,
        body: Stack(
          fit: StackFit.expand,
          children: [
            _buildTabBar(),
            Positioned(
              bottom: (FfcDataHelper().is91018Padding) ? (75 + MediaQuery.of(context).padding.bottom) : Platform.isAndroid ? 23 : (75 + MediaQuery.of(context).padding.bottom),
              right: 15,
              child: ValueListenableBuilder<TabBarModel>(
                valueListenable: currentTab,
                builder: (context, value, child) {
                  debugPrint("_buildTabBar value: ${value.currentTab.statusName}");
                  return Visibility(
                    visible: value.currentTab == FfcTab.COMMUNITY_FIND || value.currentTab == FfcTab.COMMUNITY_FOLLOW,
                    maintainState: true,
                    maintainSemantics: true,
                    maintainSize: true,
                    maintainAnimation: true,
                    child: DesPublishWidget(
                        _publishParams['publishPageUrl'] ?? "",
                        buttonImage: _publishParams['publishButtonImage'] ?? "",
                        key: Key('DesPublishWidget')),
                  );
                },
              ),
            ),
          ],
        )
    );
  }


  Widget _buildTabBar() {
    return FfcCustomTabbar(
      physicsTabBarView: NeverScrollableScrollPhysics(),
      activeTab: defaultIndex,
      onAddTabMoveTo: MoveToTab.custom,
      dynamicTabs: tabsData,
      refreshTabColor: tabStyle,
      onTabControllerUpdated: (controller) {
        tabController = controller;
        debugPrint("onTabControllerUpdated");
      },
      onTap: (index) async { // TabBar点击事件
        FfcTab tab = FfcTabExt.fromString(tabsData[index ?? 0].model?.data['tabType']);
        refreshH5Page(currentIndex,tab);
        // 上报自定义埋点c
        trackCustomPV(tab);
        // 上报Tab点击埋点数据
        FfcUtils.trackClickHome(
            context,
            tab.spmCDTab,
            tab.statusCode,
            params: {
              'tabName': tab.statusName,
              'cityCode': destinationModel.destId,
              'isChangeCity': (currentIndex == index && currentTab.value.currentTab == FfcTab.CITY) ? "true" : "false",
            });
        // 上报页面曝光
        FfcUtils.onPageEnter(context, tab);
        if (currentIndex == index && currentTab.value.currentTab == FfcTab.CITY) { // 二次点击
          // 城市选择
          Map result = await FfcUtils.goPage(context, false, pageUrl: FfcDataHelper().getCitySelectUrl, anim: Anim.none);
          // 解析 返回数据
          if (result.isNotEmpty) {
            Map<String, dynamic> destData = {};
            if (result['backdata'] != null && result['backdata'] is Map) {
              destData = getStringMap(SafeAccess.safeParseMap(result['backdata']));
            } else if (result['backdata'] != null && result['backdata'] is String) {
              destData = jsonDecode(result['backdata']);
            }
            // 更新城市名称
            if (destData.isNotEmpty) {
              // 判断是否更新城市
              if (destinationModel.destId == SafeAccess.safeParseString(
                  destData['destId'], def: "110100")) { // 用户选择相同城市，不做操作
                debugPrint("FfcCustomTabbar CitySelect result: 用户选择相同城市，不做操作");
                return;
              }
              // 上报城市更新埋点数据
              FfcUtils().trackerHome.trackCustomExposure(
                  state.context,
                  "${SPM_AB}titlebar.dest_change",
                  "dest_change_${SafeAccess.safeParseString(
                      destData['destId'], def: "110100")}",
                  {
                    'cityCode': SafeAccess.safeParseString(
                        destData['destId'], def: "110100"),
                    'cityName': SafeAccess.safeParseString(
                        destData['destName'], def: "北京")
                  });
              // 记录时效性，当前时间切换城市时间戳
              FfcDataHelper.setStorageString(FfcDataUtils.DESTINATION_CITY_CACHE_TIME_KEY, SafeAccess.safeParseString(DateTime.now().millisecondsSinceEpoch));
              // 记录用户选择的城市目的地
              FfcDataHelper.setStorageAssemble<Map>(FfcDataUtils.DESTINATION_CITY_CACHE_KEY, destData);
              destinationModel = destinationModel.copyWith(
                  destId: SafeAccess.safeParseString(
                      destData['destId'], def: "110100"),
                  destName: SafeAccess.safeParseString(
                      destData['destName'], def: "北京"),
                  type: DestinationType.City
              );
              debugPrint("FfcCustomTabbar updateTabTitle result: ${destinationModel.toJson()}");
              String url = FfcDataHelper().getDestinationUrl(destinationModel.destId, orangeUrl: tabsData[index].model?.data['url']);
              // 刷新容器
              h5ContainerCity.refreshUrl(refreshUrl: url);
              // 恢复目的地页面偏移，
              offsetH5 = 0.0;
              updateTabColor(FfcTab.CITY);
              updateTabTitle(destinationModel.sortDestName, url, index, isCity: true);
              // 更新城市名
              FfcTab.CITY.updateTabExt({
                'title': destinationModel.destName,
                'data': destData
              });
            }
          }
          debugPrint("FfcCustomTabbar CitySelect result: $result");
        }
        debugPrint("FfcCustomTabbar Tap: $index");
      },
      onDoubleTap: (index) { // TabBar双击事件
        FfcTab tab = FfcTabExt.fromString(tabsData[index ?? 0].model?.data['tabType']);
        onDoubleTap.value = onDoubleTap.value.copyWith(currentTab: tab, timestamp: DateTime.now().millisecondsSinceEpoch);
        debugPrint("FfcCustomTabbar doubleTap: $index");
      },
      onTabChanged: (index) {
        currentIndex = index ?? 0;
        FfcTab tab = FfcTabExt.fromString(tabsData[index ?? 0].model?.data['tabType']);
        if (tab == FfcTab.LOOK_WORLD) {
          h5ContainerLookWord.refreshTimeInterval(refreshTimeInterval: DateTime.now().millisecondsSinceEpoch);
        }
        updateTabColor(tab);
        debugPrint("FfcCustomTabbar Tab changed: $index");
      },
      onTabVisible: (index) {
        FfcTab tab = FfcTabExt.fromString(tabsData[index ?? 0].model?.data['tabType']);
        // 上报Tab埋点数据
        FfcUtils().trackerHome.trackExposure(
            '$SPM_AB${tab.spmCDTab}',
            tab.statusCode,
            {
              'isSelected': currentIndex == index,
              'tabName': tab.statusName,
              'cityCode': state.destinationModel.destId,
            });
        debugPrint("FfcCustomTabbar Tab Visible: $index");
      },
      trailing: FfcTitleBarMore(
          searchBar: _searchBar,
          userInfo: _userInfo,
          currentTab: currentTab
      ),
      showBackIcon: false,
      showNextIcon: false,
      isScrollable: true,
      isFillContent: true,
      labelPadding: const EdgeInsets.only(left: 15),
      indicator: FfcCustomIndicator(
          width: 27,
          height: 3,
          alignment: Alignment.bottomCenter,
          radius: BorderRadius.circular(1.5),
          padding: const EdgeInsets.only(left: 7.5, bottom: 3.5),
          color: tabStyle
      ),
    );
  }


  Widget buildTabContent(FfcTab tab, dynamic data) {
    switch (tab) {
      case FfcTab.COMMUNITY_FIND:
        List tabInfo = SafeAccess.safeParseList(data?["children"]);
        if (tabInfo.isEmpty) tabInfo = subTabData;
        return CommunityFindPage(widget.params ?? {}, tabInfo, onDoubleTap, tracker);
      case FfcTab.COMMUNITY_FOLLOW:
        return CommunityFollowPage(widget.params ?? {}, tracker);
      case FfcTab.CITY:
        h5ContainerCity.refreshUrl(
          refreshUrl: FfcDataHelper().getDestinationUrl(destinationModel.destId, orangeUrl: data['url']),
        );
        return h5ContainerCity;
      case FfcTab.COMMUNITY_VIDEO:
        return Container(
          color: Colors.red,
          height: 66,
        );
      case FfcTab.LOOK_WORLD:
        h5ContainerLookWord.refreshUrl(
          refreshUrl: FfcDataHelper().getWorldViewUrl(orangeUrl: data['url']),
        );
        return h5ContainerLookWord;
    }
  }


  void onPause(dynamic e) {
    print("fromH5PageEnter spmCnt: onPause");
    trackCustomPV(currentTab.value.currentTab);
    FfcUtils.pageStatusChange(true);
    // 页面完全不可见 上报埋点数据
    if (tracker != null) {
      tracker.onPause().then((value) {
        // 上报完成后清理 SPM记录信息
        tracker.refreshExposure();
      });
    }
  }

  void onResume(dynamic e) {
    print("fromH5PageEnter spmCnt: onResume");
    // 发送页面pageEnter
    if (!isFirst) {
      FfcUtils.onPageEnter(context, currentTab.value.currentTab, isFirst: true, onResume: true);
    }
    isFirst = false;
    viewTime = DateTime.now().millisecondsSinceEpoch;
    FfcUtils.pageStatusChange(false);
  }

  @override
  Map<String, dynamic> getFptParams() {
    // 默认社区fpid
    return {'fpid':COMMUNITY_FT_ID};
  }

  @override
  Map<String, String?> getGlobalTrackArgs() {
    return {};
  }

  @override
  String getPageName() {
    return PAGE_NAME;
  }

  @override
  String getPageSpmCnt() {
    return SPM_CNT;
  }

  @override
  FfcHomePageState get state => this;

  @override
  FfcH5Container get h5ContainerCity {
    if (_h5ContainerCity == null) {
      _h5ContainerCity = FfcH5Container(tab: FfcTab.CITY, url: FfcDataHelper().getDestinationUrl(destinationModel.destId), timeInterval: 0);
    }
    return _h5ContainerCity!;
  }

  @override
  FfcH5Container get h5ContainerLookWord {
    if (_h5ContainerLookWord == null) {
      _h5ContainerLookWord = FfcH5Container(tab: FfcTab.LOOK_WORLD, url: FfcDataHelper().getDestinationUrl(destinationModel.destId), timeInterval: 0);
    }
    return _h5ContainerLookWord!;
  }

  set h5ContainerLookWord(FfcH5Container value) {
    _h5ContainerLookWord = value;
  }
  set h5ContainerCity(FfcH5Container value) {
    _h5ContainerCity = value;
  }
}