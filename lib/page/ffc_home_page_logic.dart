import 'dart:convert';
import 'dart:io';

import 'package:fliggy_flutter_community/common/ffc_utils.dart';
import 'package:fliggy_flutter_community/components/ffc_custom_tabbar.dart';
import 'package:fliggy_flutter_community/page/components/h5_container/ffc_h5_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';
import 'ffc_home_page.dart';

/// @Package
/// @File ffc_home_page_logic
/// <AUTHOR>
/// @Description 首页逻辑处理区块
/// @Date 10-23-2024 周三 14:16

mixin FfcHomePageLogic{
  FfcHomePageState get state;
  FfcH5Container get h5ContainerLookWord;
  FfcH5Container get h5ContainerCity;
  List<TabData> get _defaultTabsData  {
    return  [
      TabData(
        model: TabTitleModel(
            data: {
              "tabType": FfcTab.LOOK_WORLD.statusCode,
              "url": FfcDataHelper().getWorldViewUrl()
            },
            title: FfcTab.LOOK_WORLD.statusName
        ),
        tabHeight: state.tabBarHeight,
        title: Text(FfcTab.LOOK_WORLD.statusName),
        contentBuilder: (context, data, title) => state.buildTabContent(FfcTab.LOOK_WORLD, data),
      ),
      TabData(
        model: TabTitleModel(
            data: {
              "tabType": FfcTab.CITY.statusCode,
              "url": FfcDataHelper().getDestinationUrl(state.destinationModel.destId)
            },
            title: state.destinationModel.sortDestName
        ),
        tabHeight: state.tabBarHeight,
        title: Text.rich(
            TextSpan(
              text: state.destinationModel.sortDestName,
              children: [
                TextSpan(
                  text: String.fromCharCode(IconData(
                    ICON_XIALAJIANTOUXIAO,
                    fontFamily: FONT_FAMILY_DEFAULT,
                  ).codePoint),
                  style: FfcTextStyle(
                    inherit: false,
                    fontSize: 14,
                    fontFamily: FONT_FAMILY_DEFAULT,
                    package: 'ficonfont',
                  ),
                ),
              ],
            )),
        contentBuilder: (context, data, title) => state.buildTabContent(FfcTab.CITY, data),
      ),
      TabData(
        model: TabTitleModel(
            data: {
              "tabType": FfcTab.COMMUNITY_FIND.statusCode
            },
            title:  FfcTab.COMMUNITY_FIND.statusName
        ),
        tabHeight: state.tabBarHeight,
        title: Text(FfcTab.COMMUNITY_FIND.statusName),
        contentBuilder: (context, data, title) => state.buildTabContent(FfcTab.COMMUNITY_FIND, data),
      ),
    ];
  }

  // 初始化数据
  void initData() {
    state.isTab = SafeAccess.safeParseBoolean(state.widget.params?['isTab'], def: true);
    if (!SafeAccess.isTextEmpty(state.widget.params?['destId'])){ // 本地LBS回传数据
      // 保存destID
      state.destinationModel = state.destinationModel.copyWith(
          destId: SafeAccess.safeParseString(state.widget.params?['destId'], def: "110100"),
          destName: SafeAccess.safeParseString(state.widget.params?['destName'], def: "北京"),
          type: DestinationType.OnLine
      );
    }
    if (_isCityTimeValid) { // 有在时效中，读取保存的目的地数据
      var destData = FfcDataHelper.getStorageAssemble(FfcDataUtils.DESTINATION_CITY_CACHE_KEY);
      if (destData is Map && !SafeAccess.isMapEmpty(destData)) {
        state.destinationModel = state.destinationModel.copyWith(
            destId: SafeAccess.safeParseString(destData['destId'], def: "110100"),
            destName: SafeAccess.safeParseString(destData['destName'], def: "北京"),
            type: DestinationType.City
        );
      }
    }

    if (!SafeAccess.isTextEmpty(state.widget.params?['sDestId'])){ // 分享数据覆盖LBS数据
      // 保存destID
      state.destinationModel = state.destinationModel.copyWith(
          destId: SafeAccess.safeParseString(state.widget.params?['sDestId'], def: "110100"),
          destName: SafeAccess.safeParseString(state.widget.params?['sDestName'], def: "北京"),
          type: DestinationType.Share
      );
    }
    // {
    //   "activityName" : "",
    //   "sceneCode" : "activity",
    //   "strategyCode" : "activity",
    //   "tabConfig" : "[{\"children\":[],\"configId\":1000038,\"indexValue\":1,\"isSelected\":false,\"level\":1,\"priority\":99,\"tabName\":\"发现\",\"tabType\":\"LookWorld\",\"url\":\"https://outfliggys.wapa.taobao.com/app/trip/rx-dest-trip-world/pages/home?disableNav=YES&from=tab&titleBarHidden=2\"},{\"children\":[],\"configId\":1000008,\"extDate\":\"{\\\"abroad\\\":false,\\\"destId\\\":110100,\\\"destName\\\":\\\"北京\\\"}\",\"indexValue\":2,\"isSelected\":false,\"level\":1,\"priority\":99,\"tabName\":\"目的地\",\"tabType\":\"City\",\"url\":\"https://outfliggys.wapa.taobao.com/app/trip/rx-dest-2024/pages/detail?titleBarHidden=2&disableNav=YES&from=tab&destId=110100\"},{\"children\":[{\"children\":[],\"configId\":1000003,\"indexValue\":1,\"isSelected\":false,\"level\":2,\"parentId\":1000000,\"priority\":999,\"tabId\":1,\"tabName\":\"旅行吧\"},{\"children\":[],\"configId\":1000004,\"indexValue\":2,\"isSelected\":false,\"level\":2,\"parentId\":1000000,\"priority\":999,\"tabId\":82,\"tabName\":\"会员吧\"},{\"children\":[],\"configId\":1000005,\"indexValue\":3,\"isSelected\":false,\"level\":2,\"parentId\":1000000,\"priority\":2,\"tabId\":83,\"tabName\":\"福利吧\"},{\"children\":[],\"configId\":1000021,\"indexValue\":4,\"isSelected\":false,\"level\":2,\"parentId\":1000000,\"priority\":1,\"tabId\":84,\"tabName\":\"飞行吧\"},{\"children\":[],\"configId\":1000020,\"indexValue\":5,\"isSelected\":false,\"level\":2,\"parentId\":1000000,\"priority\":1,\"tabId\":20380035,\"tabName\":\"攻略吧\"}],\"configId\":1000000,\"indexValue\":3,\"isSelected\":true,\"level\":1,\"priority\":999,\"tabName\":\"社区\",\"tabType\":\"CommunityFind\"}]",
    //   "tips" : ""
    // }
    // 解析透传的Tab数据
    var tabInfo = [];
    if (state.widget.params?['tabConfig'] != null && state.widget.params?['tabConfig'] is String) {
      tabInfo = jsonDecode(state.widget.params?['tabConfig']);
    } else if (state.widget.params?['tabConfig'] != null && state.widget.params?['tabConfig'] is Map) {
      tabInfo = state.widget.params?['tabConfig'];
    }
    if (tabInfo is List && !SafeAccess.isListEmpty(tabInfo)) {
      // 初始化Tab
      state.tabsData = convertTab(tabInfo);
      state.isTabLoad = true;
      // 缓存Tab
      FfcDataHelper.setStorageAssemble<List>(FfcDataUtils.DESTINATION_TAB_CACHE_KEY, tabInfo);
    } else {
      // 初始化Tab数据
      tabInfo = FfcDataHelper.getStorageAssemble(FfcDataUtils.DESTINATION_TAB_CACHE_KEY) ?? [];
      if (tabInfo is List && !SafeAccess.isListEmpty(tabInfo)) {
        // 初始化Tab
        state.tabsData = convertTab(tabInfo);
      }else{
        state.tabsData = _defaultTabsData;
      }
    }
    if (tabInfo is List && !SafeAccess.isListEmpty(tabInfo)) {
      // 初始化Tab选中
      state.defaultIndex = tabInfo.indexOf(tabInfo.firstWhere((element) => element['isSelected'] == true, orElse: () => tabInfo.first));
      state.currentIndex = state.defaultIndex;
      updateTabColor(FfcTabExt.fromString(tabInfo[state.currentIndex]["tabType"] ?? ''));
    }
    // 初始化 容器
    didUpdateH5Container();
    //
    state.tabStyle = ValueNotifier<TabColor>(
      TabColor(
          labelStyle: FfcTextStyle(
              fontFamily: 'PingFang SC',
              fontWeight: FontWeight.w500,
              fontSize: fd.font_size_large_title,
              color: fd.color_darkgray,
              height: 1.2),
          labelColor: state.currentTab.value.titleBarColor,
          unselectedLabelStyle: FfcTextStyle(
              fontFamily: 'PingFang SC',
              fontWeight: FontWeight.w500,
              fontSize: fd.font_size_middle_title,
              color: fd.color_darkgray,
              height: 1.2),
          unselectedLabelColor: state.currentTab.value.titleBarColor,
          indicatorColor: state.currentTab.value.titleBarColor
      ),
    );
    // 更新Tab选中
    if(!SafeAccess.isTextEmpty(state.widget.params?['sTabCode'])){
      updateTabColor(FfcTabExt.fromString(state.widget.params?['sTabCode'] ?? ''));
      state.defaultIndex = state.tabsData.indexOf(state.tabsData.firstWhere((element) => element.model?.data['tabType'] == state.widget.params?['sTabCode'], orElse: () => state.tabsData.first));
      state.currentIndex = state.defaultIndex;
    }
  }

  didUpdateH5Container() {
    // 初始化 容器
    state.tabsData.forEach((element) {
      if (element.model?.data['tabType'] == FfcTab.CITY.statusCode) {
        state.h5ContainerCity = FfcH5Container(tab: FfcTab.CITY, url: FfcDataHelper().getDestinationUrl(state.destinationModel.destId, orangeUrl:element.model?.data['url'] ?? ""), timeInterval: 0);
      }
      if (element.model?.data['tabType'] == FfcTab.LOOK_WORLD.statusCode) {
        state.h5ContainerLookWord = FfcH5Container(tab: FfcTab.LOOK_WORLD, url: FfcDataHelper().getWorldViewUrl(orangeUrl: element.model?.data['url'] ?? ""), timeInterval: 0);
      }
    });
  }


  // 判断城市可用时效性，是否大于14天
  // 返回false 可以在接口刷新目的地数据，否则仅可以使用用户上次主动锚定的数据
  bool get _isCityTimeValid {
    String? time = FfcDataHelper.getStorageString(FfcDataUtils.DESTINATION_CITY_CACHE_TIME_KEY);
    if (SafeAccess.isTextEmpty(time)) {
      return false;
    }
    int timeStamp = SafeAccess.safeParseInt(time);
    int currentTime = DateTime.now().millisecondsSinceEpoch;
    // 判断是否大于14天
    return (currentTime - timeStamp) < 14 * 24 * 60 * 60 * 1000;
  }

  void updateTabColor(FfcTab tab) {
    if (tab == FfcTab.CITY) {
      Color color = tab.tabExt['tabColor'];
      bool canSetColor = SafeAccess.safeParseBoolean(tab.tabExt['canSetColor']);
      if (state.offsetH5 > 0.5 && state.currentTab.value.titleBarColor != fd.color_darkgray && canSetColor) {
        color = fd.color_darkgray;
        updateStatusBarBrightness('dark');
      } else if (state.offsetH5 < 0.5 && state.currentTab.value.titleBarColor != fd.color_white && canSetColor) {
        color = fd.color_white;
        updateStatusBarBrightness('light');
      }
      state.currentTab.value = state.currentTab.value.copyWith(
        currentTab: tab,
        titleBarColor: color,
      );
      state.tabStyle.value = state.tabStyle.value.copyWith(labelColor: color, unselectedLabelColor: color, indicatorColor: color);
    } else {
      updateStatusBarBrightness('dark');
      state.currentTab.value = state.currentTab.value.copyWith(
        currentTab: tab,
        titleBarColor: tab.tabExt['tabColor'],
      );
      state.tabStyle.value = state.tabStyle.value.copyWith(labelColor: fd.color_darkgray, unselectedLabelColor: fd.color_darkgray, indicatorColor: fd.color_darkgray);
    }
  }

  // 更新Tab
  void updateTabData(List<TabData> tabData) {
    state.safeSetState(() {
      state.tabsData = tabData;
    });
  }

  // 更新Tab 标题文案 H5链接
  void updateTabTitle(String title, String url, int index, {bool isCity = false}) {
    state.safeSetState(() {
      Map data = state.tabsData[index].model?.data;
      data['url'] = url;
      // 城市特殊处理
      if (isCity) {
        state.tabsData[index] = state.tabsData[index].copyWith(
            model: state.tabsData[index].model?.copyWith(data: data, title: title),
            title: Text.rich(
                TextSpan(
                  text: title,
                  children: [
                    TextSpan(
                      text: String.fromCharCode(IconData(
                        ICON_XIALAJIANTOUXIAO,
                        fontFamily: FONT_FAMILY_DEFAULT,
                      ).codePoint),
                      style: FfcTextStyle(
                        inherit: false,
                        fontSize: 14,
                        fontFamily: FONT_FAMILY_DEFAULT,
                        package: 'ficonfont',
                      ),
                    ),
                  ],
                )));
        return;
      }
      state.tabsData[index] = state.tabsData[index].copyWith(
          model: state.tabsData[index].model?.copyWith(data: data, title: title),
          title: Text(title));
    });
  }



  // Tab Map 转为TabData List
  List<TabData> convertTab(List tabInfo) {
    state.safeSetState(() {
      // 二次刷新 发现Tab数据
      state.subTabData = SafeAccess.safeParseList(tabInfo.firstWhere((element) => element['tabType'] == 'CommunityFind', orElse: () => {})?['children']);
    });
    // 解析 Tab
    List<TabData> tabData = tabInfo.asMap().entries.map((e) {
      // 解析Tab中标识选中的Tab index
      FfcTab tab = FfcTabExt.fromString(e.value['tabType']);
      // 判断当前服务端下发是否有URL
      if (SafeAccess.isTextEmpty(e.value['url'])) {
        if (tab == FfcTab.LOOK_WORLD) {
          e.value['url'] = FfcDataHelper().getWorldViewUrl();
        } else if (tab == FfcTab.CITY) {
          e.value['url'] = FfcDataHelper().getDestinationUrl(state.destinationModel.destId);
        }
      }
      // 城市Tab 特殊处理名称
      if (tab == FfcTab.CITY) {
        Map destData = {};
        if (e.value['extDate'] != null && e.value['extDate'] is Map) {
          destData = SafeAccess.safeParseMap(e.value['extDate']);
        } else if (e.value['extDate'] != null && e.value['extDate'] is String) {
          destData = jsonDecode(e.value['extDate']);
        }
        // 判断当前类型不是可以在线跟新 的状态。此刻不进行目的地更新
        // 非OnLine类型 ，且用户选择城市时效么有过期
        if (state.destinationModel.type == DestinationType.OnLine && !_isCityTimeValid) {
          // 更新城市名称
          state.destinationModel = state.destinationModel.copyWith(
              destId: SafeAccess.safeParseString(
                  destData['destId'], def: "110100"),
              destName: SafeAccess.safeParseString(
                  destData['destName'], def: "北京"),
              type: DestinationType.OnLine
          );
        }
        // 更新城市名
        tab.updateTabExt({
          'title': state.destinationModel.destName,
          'data': destData
        });
      } else {
        tab.updateTabExt({
          "title": e.value['tabName'],
          "data": e.value
        });
      }
      // 重置选中
      if (SafeAccess.safeParseBoolean(e.value['isSelected']) && state.destinationModel.type != DestinationType.Share) {
        state.defaultIndex = e.key;
        debugPrint("_buildTabBar value1: ${state.currentTab.value.currentTab.statusName}");
        updateTabColor(tab);
      }

      return TabData(
        model: TabTitleModel(
          data: e.value,
          title: (tab == FfcTab.CITY) ? state.destinationModel.sortDestName : tab.statusName,
        ),
        tabHeight: state.tabBarHeight,
        title: (tab == FfcTab.CITY) ? Text.rich(
            TextSpan(
              text: state.destinationModel.sortDestName,
              children: [
                TextSpan(
                  text: String.fromCharCode(IconData(
                    ICON_XIALAJIANTOUXIAO,
                    fontFamily: FONT_FAMILY_DEFAULT,
                  ).codePoint),
                  style: FfcTextStyle(
                    inherit: false,
                    fontSize: 14,
                    fontFamily: FONT_FAMILY_DEFAULT,
                    package: 'ficonfont',
                  ),
                ),
              ],
            )) : Text(tab.statusName),
        contentBuilder: (context, data, title) => state.buildTabContent(tab, data),
      );
    }).toList();
    // 纠正初始化数据
    if (tabData[state.defaultIndex].model?.data['tabType'] != state.currentTab.value.currentTab.statusCode) {
      FfcTab currentTab = FfcTabExt.fromString(tabData[state.defaultIndex].model?.data['tabType']);
      updateTabColor(currentTab);
    }
    return tabData;
  }

  // 上报页面埋点信息
  void trackCustomPV(FfcTab tab) {
    int viewPageTime = DateTime.now().millisecondsSinceEpoch - state.viewTime;
    if (viewPageTime <= 200) return;
    state.viewTime = DateTime.now().millisecondsSinceEpoch;
    FfcUtils.trackCustom(state.context, 'page_stay_time', params: {
      "spm": "${tab.spmABCPage}.0",
      "viewTime": "$viewPageTime",
    });
  }

  // 更新状态栏
  void updateStatusBarBrightness(String statusBarMode) {
    if (statusBarMode == 'light') {
      // _statusBarMode = 'light';
      SystemChrome.setSystemUIOverlayStyle(FfcDataUtils.light);
    } else if (statusBarMode == 'dark') {
      // _statusBarMode = 'dark';
      SystemChrome.setSystemUIOverlayStyle(FfcDataUtils.dark);
    }
  }

  // 刷新H5页面
  void refreshH5Page(int currentIndex, FfcTab tab) async {
    if (!(FfcDataHelper().isIOS18_2Fix)) return;
    if (state.isProcessing) return;
    state.isProcessing = true;
    if (tab == FfcTab.CITY) {
      String url = FfcDataHelper().getDestinationUrl(state.destinationModel.destId, orangeUrl: state.tabsData[currentIndex].model?.data['url']);
      print('isIOS18_2Fix: refreshH5PageCITY: url: $url');
      // 刷新容器
      h5ContainerCity.reloadUrl(
          refreshUrl: url
      );
    }
    if (tab == FfcTab.LOOK_WORLD) {
      String url = FfcDataHelper().getWorldViewUrl(orangeUrl: state.tabsData[currentIndex].model?.data['url']);
      print('isIOS18_2Fix: refreshH5PageLOOK_WORLD: url: $url');
      h5ContainerLookWord.reloadUrl(
          refreshUrl: url
      );
    }
    // 完成后，设置处理状态为 false
    Future.delayed(const Duration(milliseconds: 300), () {
      state.isProcessing = false;
    });
  }
}