import 'dart:convert';

import 'package:fliggy_flutter_community/common/ffc_utils.dart';
import 'package:flutter/cupertino.dart';

import '../common/common_safe_access_util.dart';
import 'ffc_home_page.dart';
import 'ffc_play_map_page.dart';

/// 社区 / 玩法地图分流决定页
class FfcMiddleDiversionPage extends StatefulWidget {
  final Map? params;
  const FfcMiddleDiversionPage({
    Key? key,
    this.params
  }): super(key: key);

  @override
  State<FfcMiddleDiversionPage> createState() => _FfcMiddleDiversionPageState();
}

class _FfcMiddleDiversionPageState extends State<FfcMiddleDiversionPage> {

  bool _showPlayMap = false;
  @override
  void initState() {
    super.initState();
    _getTabParams();
  }

  @override
  Widget build(BuildContext context) {
    // if(_showPlayMap == null) return SizedBox.shrink();

    if(_showPlayMap) {
      // 打开玩法地图页
      return FfcPlayMapPage(params: widget.params);
    } else {
      // 打开社区页
      return FfcHomePage(title: "飞猪社区", params: widget.params ?? {});
    }
  }

  void _getTabParams() {
    // 先从 widget.params取tabConfig判断展示地图还是社区页
    List tabInfoList = [];
    String tabConfigStr = CommonSafeAccess.safeParseString(widget.params?['tabConfig']) ?? '';
    if (tabConfigStr.isNotEmpty) {
      tabInfoList = jsonDecode(tabConfigStr);
    } else {
      List tabConfigList = CommonSafeAccess.safeParseList(widget.params?['tabConfig']);
      if(tabConfigList.isNotEmpty) {
        tabInfoList = tabConfigList;
      }
    }
    if(tabInfoList.isNotEmpty) {
      _showPlayMap = _gotoPlayMap(tabInfoList);
      return;
    }

    //如果取不到值，再请求接口获取tabConfig去判断后刷新页面
    TitleBarNet.requestCommunityTab(context).then((value) {
      if (value.success) {
        List tabInfo = CommonSafeAccess.safeParseList(value.data["data"]);
        if (CommonSafeAccess.isListEmpty(tabInfo)) return;
        _showPlayMap = _gotoPlayMap(tabInfo);
      } else {
        _showPlayMap = false;
      }
      safeSetState(() { });
    }).onError((error, stackTrace) {
      _showPlayMap = false;
      safeSetState(() { });
    });
}

  bool _gotoPlayMap(List tabInfoList) {
    for(Map tab in tabInfoList) {
      String tabType = SafeAccess.safeParseString(tab['tabType']) ?? '';
      if(tabType == 'play') return true;
    }
    return false;
  }
}
