import 'dart:async';
import 'dart:convert';
import 'dart:core';
import 'dart:io';
import 'package:fliggy_flutter_community/common/collection_data_util.dart';
import 'package:fliggy_flutter_community/common/ffc_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fliggy_flutter_map/fliggy_flutter_common_map.dart';

import 'package:fliggy_flutter_login/fliggy_login.dart';

import '../common/common_safe_access_util.dart';
import '../common/text_util.dart';
import '../fliggy_flutter_community.dart';
import '../model/ffc_user_info.dart';
import 'fliggy_play_map/comment/new_guider_mask.dart';
import 'fliggy_play_map/ffc_poi_overlay.dart';
import 'fliggy_play_map/marker/marker_factory.dart';
import 'fliggy_play_map/model/play_map_model.dart';
import 'fliggy_play_map/net_work/play_map_net.dart';
import 'fliggy_play_map/play_map_tabbar.dart';
import 'fliggy_play_map/utils/play_map_constant.dart';

part 'fliggy_play_map/ffc_play_map_process_part.dart';
part 'fliggy_play_map/net_work/ffc_play_map_network_part.dart';
part 'fliggy_play_map/marker/ffc_play_map_marker_part.dart';
/// 工具类部分
part 'fliggy_play_map/utils/ffc_play_map_utils_part.dart';

/// 包含 widget 部分
part 'fliggy_play_map/ffc_play_map_widget_part.dart';
class FfcPlayMapPage extends StatefulWidget {
  final Map? params;

  const FfcPlayMapPage({Key? key, this.params}) : super(key: key);

  @override
  State<FfcPlayMapPage> createState() => _FfcPlayMapPageState();
}

const String kTagImageURL = 'https://gw.alicdn.com/imgextra/i2/O1CN01BTckHy1q4UFv9hopr_!!6000000005442-2-tps-84-84.png';

class _FfcPlayMapPageState extends State<FfcPlayMapPage>
    with TickerProviderStateMixin, FliggyPageMixin, FexpTrackContext {
  /// 地图的控制器
  final GlobalKey<FliggyCommonMapState> _mapController =
      GlobalKey<FliggyCommonMapState>();

  /// tab数据
  List<PlayMapTabModel> _tabs = [];
  PlayMapTabModel? _selectedTabModel;
  PlayMapTabModel? _selectedGroupModel;

  /// tab数据是否加载完成
  bool _tabDataLoaded = false;

  /// 用户信息数据
  FFCUserInfoModel? _userInfoModel;

  /// 用户当前定位位置
  ValueNotifier<LatLng?> _userLbs = ValueNotifier<LatLng?>(null);

  // 常住城市
  CityModel? _permanentCity;

  // 当前定位城市ID
  num? _lbsCityId;

  // 当前定位与常住国家是否相同
  bool _abroad = false;

  // 当前定位与常住城市是否相同
  bool _differentCity = false;

  // 群聊信息
  ValueNotifier<ChatGroupModel?> _chatGroupInfo =
      ValueNotifier<ChatGroupModel?>(null);

  ValueNotifier<bool> _showPOIPop = ValueNotifier<bool>(false);
  ChatGroupModel? _tempGroupInfo;

  // 是否要请求玩法信息
  bool _shouldRequestPlayInfo = false;

  // 是否为重置地图位置
  bool _reloadPosition = false;

  bool _isFirst = true;

  bool _showNewGuider = false;

  PlayInfoModel? selectPOIInfo;

  /// 玩法数据Map
  Map<String, List<PlayInfoModel>> _playInfoMap = {};

  /// 玩法数据对应的Point Map
  List<CommonPointGroupModel> _pointGroupList = [];

  /// 所有玩法point组成的List
  List<CommonPointModel> get _playInfoPointList {
    return _pointGroupList.expand((group) => group.pointList).toList();
  }

  /// 地图Marker数据来源
  List<CommonPointGroupModel> get _markerDataSource {
    List<CommonPointGroupModel> dataSource = [];
    //增加用户lbs marker
    dataSource.safeAdd(_userPointGroup);
    // 增加玩法 marker
    if (_pointGroupList.isNotEmpty) {
      dataSource.addAll(_pointGroupList);
    }
    return dataSource;
  }

  CommonPointGroupModel get _userPointGroup {
    List<CommonPointModel> pointList = [];
    if (_userPoint != null) {
      pointList.add(_userPoint!);
    }
    return CommonPointGroupModel(MarkerTypeUser, pointList);
  }

  FliggyCustomStyleOptions? customstyle;
  @override
  void initState() {
    super.initState();
    // 前置获取定位信息
    scheduleMicrotask((){
      _mapCustomStyle().then((value) {
        customstyle = value;
      });
    });

    _getUserLbs().then((value) {
      _userLbs.value = value;
    });

    // 生命周期与埋点
    FfcUtils().trackerHome = tracker;
    // 从红点中获取tabData
    _initTabData();
    // 从红点中获取常住地信息
    _initPermanentCity();
    // 注册监听
    _registerNotifier();
    // 请求用户信息
    _requestUserInfo();

    // 初始化新人引导参数
    _showNewGuider = CommonSafeAccess.safeParseBoolean(FfcDataHelper.getStorageAssemble(FfcDataUtils.NEW_GUIDER_KEY))?? true;

    // 初始化地图位置
    // _initMapPosition();
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Stack(
        children: [
          _buildMap(),
          if (_tabs.isNotEmpty)
            PlayMapTabBar(
              tabs: _tabs,
              userInfo: _userInfoModel,
              permanentCity: _permanentCity,
              onUserIconClicked: () async {
                // 点击tabBar用户头像
                FfcUtils.trackClickPlayMap(
                    context, 'category_bar.usericon', 'category_bar.usericon',
                    params: {'city_id': _permanentCity?.cityId ?? ''});
                _userIconClicked();
                cancelSelectedPOI();
              },
              onTabClicked: (tabModel, groupModel) {
                // 重新加载地图状态
                _playInfoMap = {};
                _selectedTabModel = tabModel;
                _selectedGroupModel = groupModel;
                _reloadMapPosition();
                cancelSelectedPOI();
              },
              onGroupClicked: (tabModel, groupModel) {
                // 重新加载地图状态
                _playInfoMap = {};
                _selectedTabModel = tabModel;
                _selectedGroupModel = groupModel;
                _reloadMapPosition();
                cancelSelectedPOI();
              },
              canBack: SafeAccess.safeParseBoolean(widget.params?['back_enable']) ?? false,
            ),

          // 定位按钮
          _buildUserLocationButton(),
          // 群聊按钮
          _buildChatGroupButton(),

          ValueListenableBuilder(
              valueListenable: _showPOIPop,
              builder: (context, bool value, child) {
                if (value) {
                  if (selectPOIInfo != null) {
                    return POIPop(selectPOIInfo!, () {
                      cancelSelectedPOI();
                    },_userLbs.value?.latitude.toString() ?? null, _userLbs.value?.longitude.toString() ?? null,
                      SafeAccess.safeParseBoolean(widget.params?['back_enable'])?? false,
                    );
                  }
                }
                return SizedBox.shrink();
              }),

          if (_showNewGuider)
            NewGuiderMask(
              finishCallback: () {
                // 用户看完新手引导,以后再也不给看了
                _showNewGuider = false;
                FfcDataHelper.setStorageAssemble(FfcDataUtils.NEW_GUIDER_KEY, false);
              },
            )

        ],
      ),
    );
  }

  /// ============= init start ========
  /// 初始化tab 信息
  void _initTabData() {
    // 1. 先从红点里拿tab数据
    List tabInfoList = [];
    String tabConfigStr =
        CommonSafeAccess.safeParseString(widget.params?['tabConfig']) ?? '';
    if (tabConfigStr.isNotEmpty) {
      tabInfoList = jsonDecode(tabConfigStr);
    } else {
      List tabConfigList =
          CommonSafeAccess.safeParseList(widget.params?['tabConfig']);
      if (tabConfigList.isNotEmpty) {
        tabInfoList = tabConfigList;
      }
    }
    if (tabInfoList.isNotEmpty) {
      _initSelectedTab(tabInfoList);
      _tabDataLoaded = true;
      // 缓存Tab
      FfcDataHelper.setStorageAssemble<List>(
          FfcDataUtils.PLAY_MAP_TAB_CACHE_KEY, tabInfoList);
      return;
    }
    // 2. 红点内没有数据再从缓存中获取
    tabInfoList =
        FfcDataHelper.getStorageAssemble(FfcDataUtils.PLAY_MAP_TAB_CACHE_KEY) ??
            [];
    if (tabInfoList.isNotEmpty) {
      _initSelectedTab(tabInfoList);
    }
    // 3.然后再重新调用tab接口获取数据
    _requestTabData();
  }

  void _initPermanentCity() {
    // 从红点中获取常住地信息
    String permanentCityStr =
        CommonSafeAccess.safeParseString(widget.params?['permanentCity']) ?? '';
    if (permanentCityStr.isNotEmpty) {
      Map permanentCityMap = jsonDecode(permanentCityStr);
      _permanentCity = CityModel.fromJson(permanentCityMap);
    }
  }

  // 注册监听
  void _registerNotifier() {
    // 监听登录
    FliggyLoginApi.getInstance().registerLoginReceiver(_loginChanged);
    // 监听用户lbs改变
    _userLbs.addListener(_userLbsChanged);
  }

  /// 初始化地图位置
  Future<void> _initMapPosition() async {
    debugPrint('muxian --- initMapPosition start');
    // 1.获取用户当前定位

    bool hasReload = false;
    // 如果已经有常住地信息,在异地信息不重要的情况下,可以先行移动一下地图,减少加载等待时间
    if (_permanentPoint != null || _userPoint != null) {
      _reloadMapPosition();
      hasReload = true;
    }
    // 2. 请求用户常住地逻辑
    await _requestPermanent();
    // 3. 设置地图位置
    if (!hasReload) {
      _reloadMapPosition();
    }
  }

  void _loginChanged(LoginNotify type, int requestCode) {
    _requestUserInfo(); // 强制刷新用户信息
  }

  void _userLbsChanged() {
    _mapController.currentState?.updateMarkerGroup(_userPointGroup);
  }

  void _requestTabData() {
    TitleBarNet.requestCommunityTab(context).then((value) {
      if (value.success) {
        List tabInfo = CommonSafeAccess.safeParseList(value.data["data"]);
        if (CommonSafeAccess.isListEmpty(tabInfo)) return;
        // 缓存Tab
        FfcDataHelper.setStorageAssemble<List>(
            FfcDataUtils.PLAY_MAP_TAB_CACHE_KEY, tabInfo);
        safeSetState(() {
          _initSelectedTab(tabInfo);
        });
      }
    }).catchError((error) {
      return new MtopResponseModel();
    });
  }

  void _initSelectedTab(List tabInfo) {
    _tabs = _generateTabData(tabInfo);
    // 初始化默认选中tab和Group
    if (_tabs.isNotEmpty) {
      _selectedTabModel = _tabs.first;
      if (SafeAccess.isListNotEmpty(_selectedTabModel?.children)) {
        _selectedGroupModel = _selectedTabModel?.children?.first ?? null;
      }
    }
  }

  List<PlayMapTabModel> _generateTabData(List tabInfo) {
    List<PlayMapTabModel> tabList = [];
    for (var tab in tabInfo) {
      Map tabMap = CommonSafeAccess.safeParseMap(tab);
      PlayMapTabModel tabModel = PlayMapTabModel.fromJson(tabMap);
      if (tabModel.tabName.isNotEmpty && tabModel.tabType.isNotEmpty)
        tabList.add(tabModel);
      // tabList.add(tabModel);
    }
    return tabList;
  }

  /// 获取用户定位
  Future<LatLng?> _getUserLbs() async {
    bool hasPermission = await CommonMapTools.checkUserLbsPermission(context);
    if (hasPermission) {
      return await CommonMapTools.getUserLbs(context);
    } else {
      return null;
    }
  }

  /// ============= init end ========

  void _requestPlayInfoHandler(CameraPosition currentPosition, MapScreenRadius? radius) {
    // 根据当前地图位置请求对应玩法
    _requestPlayInfo(currentPosition.target, currentPosition.zoom,
        radius?.diagonalRadius ?? 100,
        needUpdateCamera: _reloadPosition);
    // 重置地图flag请求过一次后就置为false，等待下次需要重置地图的时候再置为true
    _reloadPosition = false;
    // 玩法请求过一次后就置为false，等待下次需要请求时再置为true
    // 宝运备注:这个字段叫地图是否移动更贴切一点,因为现在还没有不请求的情况
    _shouldRequestPlayInfo = false;
  }

  void cancelSelectedPOI () {
    if (_showPOIPop.value) {
      // 1.更新玩法数据
      List<PlayInfoModel>? infoList = _playInfoMap['poiCards'];
      for (PlayInfoModel model in infoList ?? []) {
        model.isSelected = false;
      }
      List<PlayInfoModel>? infoList2 = _playInfoMap['playCards'];
      for (PlayInfoModel model in infoList ?? []) {
        model.isSelected = false;
      }


      // 2. 更新对应的point List
      _updatePointData();

      // 4. 刷新地图Marker
      Future.delayed(Duration(milliseconds: 10), () {
        _mapController.currentState?.updateAllMarker();
      });
      _showPOIPop.value = false;
    } else {
      List<PlayInfoModel>? infoList2 = _playInfoMap['playCards'];
      for (PlayInfoModel model in infoList2 ?? []) {
        model.isSelected = false;
      }
    }
  }

  CameraPosition? _initialCameraPosition() {
    if (_permanentCity?.latitude != null && _permanentCity?.longitude != null) {
      LatLng initLatLng =
          LatLng(_permanentCity!.latitude!, _permanentCity!.longitude!);
      return CameraPosition(target: initLatLng, zoom: 9);
    }
    return null;
  }

  /// 重置地图位置
  void _reloadMapPosition() {
    //1. 获取地图需要重置到的位置
    List<CommonPointModel> points = [];
    // 如果用户在国外，则地图定位到用户常驻地
    if (_abroad) {
      points.safeAdd(_permanentPoint);
    } else if (_differentCity) {
      // 如果为国内异地，则定位到常驻地和用户当前lbs都能展示下的区域
      // 一期：异地情况下暂时只展示常住地区域
      points.safeAdd(_permanentPoint);
      // points.safeAdd(_userPoint);
    } else if (_userPoint != null) {
      // 如有定位的话，定位到用户当前lbs
      points.safeAdd(_userPoint);
    } else {
      // 其他情况定位到常住地
      points.safeAdd(_permanentPoint);
    }
    _shouldRequestPlayInfo = true;
    _reloadPosition = true;
    // 地图视角重置时，要先清空地图上的Marker
    // _playInfoPointMap.clear();
    // _mapController.currentState?.updateAllMarker();
    // 如有目标位置，将地图移动到对应位置
    if (points.isNotEmpty) {
      _mapController.currentState
          ?.updateCameraWithPoints(pointList: points, animated: false, duration: 50);
    } else {
      // 如没有目标位置，则直接就地请求玩法
      LatLng? latLng = _mapController.currentState?.currentMapPosition?.target;
      double? zoom = _mapController.currentState?.currentMapPosition?.zoom;
      double? radius = _mapController.currentState?.currentRadius?.heightRadius;
      if (latLng != null && zoom != null && radius != null) {
        _requestPlayInfo(latLng, zoom, radius);
      }
    }
  }

  /// ============= map end ========

  /// ============= event handle start ========
  Future<void> _userIconClicked() async {
    bool isLogin = await FfcUtils.hasLogin();
    if (!isLogin) {
      FfcUtils.login();
    } else {
      if (_userInfoModel?.userJumpUrl == null) {
        return;
      }
      FfcUtils.goPage(context, false,
          pageUrl: _userInfoModel?.userJumpUrl ?? '');
    }
  }

  /// ============= event handle end ========

  /// ============= page event start ========

  @override
  void onPause() {
    super.onPause();
    debugPrint('onPause');
  }

  @override
  Future<void> onResume() async {
    super.onResume();
    // 如果切回页面后用户lbs数据发生变化，则重新设置地图视角
    LatLng? currentLbs = await _getUserLbs();
    if (currentLbs != _userLbs.value) {
      _userLbs.value = currentLbs;
      await _initMapPosition();
    }
  }

  @override
  String getPageName() {
    return PAGE_NAME;
    return PlayMapConstant.page_NAME;
  }

  @override
  String getPageSpmCnt() {
    return PlayMapConstant.spm_CNT;
  }

  @override
  Map<String, dynamic> getFptParams() {
    return {};
  }

  @override
  Map<String, String?> getGlobalTrackArgs() {
    return {};
  }

  /// ============= page event end ========
}
