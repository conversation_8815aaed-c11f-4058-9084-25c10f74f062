import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:fliggy_flutter_community/page/components/card/community_tab.dart';
import 'package:fliggy_flutter_community/page/components/factory/community_feeds_factory.dart';
import 'package:fliggy_flutter_community/page/components/feeds_widget/community/community_common_bubble.dart';
import 'package:fliggy_flutter_community/page/components/header/ffc_title_bar_more.dart';
import 'package:fliggy_flutter_community/page/components/header/fliggy_refrsh_header.dart';
import 'package:fliggy_flutter_community/page/components/publish/des_publish.dart';
import 'package:flutter/material.dart';
import 'package:fliggy_flutter_community/common/ffc_utils.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';
import 'package:ffperformance/ffperformance.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:visibility_detector/visibility_detector.dart';
/// <AUTHOR>
/// @date Created on 2023/8/19
/// @email <EMAIL>
/// @company Alibaba Group
/// @description 社区发现页

class CommunityFindPage extends StatefulWidget {
  final Map params;
  /// 二级Tab数据
  final List tabInfo;
  final FexpPageTracker tracker;
  final ValueNotifier<TabBarModel> onDoubleTap;

  CommunityFindPage(this.params, this.tabInfo, this.onDoubleTap, this.tracker);

  @override
  State<CommunityFindPage> createState() => _CommunityFindPageState();
}

class _CommunityFindPageState extends State<CommunityFindPage>
    with AutomaticKeepAliveClientMixin, SingleTickerProviderStateMixin {
  /// 无尽流组件
  final PagingController<int, dynamic> _pagingController =
  PagingController(firstPageKey: 1);
  final ScrollController _scrollController = ScrollController();
  RefreshController _refreshController = RefreshController();
  /// 定坑内容ID 传给服务端
  String _contentId = "";
  String _pushTrack = '';

  /// feeds流推荐参数 服务端下发
  Map _paging = {};
  String _searchId = "";
  ValueNotifier<int> _tabId = ValueNotifier<int>(1);

  Map _cacheData = {};

  // 默认请求分页大小
  static const _pageSize = 10;
  @override
  bool get wantKeepAlive => true;

  /// 气泡文字
  String _tipBubbleMessage = "";

  @override
  void initState() {
    // 初始化下啦刷新请求
    _pagingController.addPageRequestListener(_fetchPage);
    super.initState();
    _initParams();
    _loadCache();
    if (_getSelectedIndex == -1 && widget.tabInfo.isNotEmpty) {
      widget.tabInfo[0]['isSelected'] = true;
    }
    widget.onDoubleTap.addListener(_onDoubleTap);

    // 刷新数据
    _fetchPage(1);

    // 刷新 全局发现回调监听
    FfcUtils.changePostRefreshListener(context, FfcTab.COMMUNITY_FIND, (value) {
      if (value != null) {
        String contentId = SafeAccess.safeParseString(value["contentId"]);
        _tabId.value = SafeAccess.safeParseInt(value["tabId"], def: _tabId.value);
        if (!SafeAccess.isTextEmpty(contentId)) { // 以果有contentId 刷新
          _contentId = contentId;
          _pagingController.refresh();
        }
        debugPrint("popContentPublish content: $_contentId tab: $_tabId");
      }
    });

    // 拦截旧版本 H5刷新回调通知
    FBroadcast.instance("native").register("popContentPublish", (value, callback) {
      if (value != null) {
        String contentId = SafeAccess.safeParseString(value["contentId"]);
        _tabId.value  = SafeAccess.safeParseInt(value["tabId"], def: _tabId.value);
        if (!SafeAccess.isTextEmpty(contentId)) {
          _contentId = contentId;
        }
        _pagingController.refresh();
        debugPrint("popContentPublish content: $_contentId tab: $_tabId");
      }
    });
  }

  void _onDoubleTap() {
    // 双击 // TabBar双击事件
    if (widget.onDoubleTap.value.currentTab == FfcTab.COMMUNITY_FIND) {
      // 回到顶部
      _scrollController.animateTo(0, duration: Duration(milliseconds: 300), curve: Curves.ease);
    }
  }

  @override
  void dispose() {
    FfcUtils.changePostRefreshUnListener(FfcTab.COMMUNITY_FIND, context);
    widget.onDoubleTap.removeListener(_onDoubleTap);
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant CommunityFindPage oldWidget) {
    if (oldWidget.params != widget.params) {
      _initParams();
    }
    if (oldWidget.tabInfo != widget.tabInfo) {
      if (_getSelectedIndex == -1 && widget.tabInfo.isNotEmpty) {
        widget.tabInfo[0]['isSelected'] = true;
      }
    }
    super.didUpdateWidget(oldWidget);
  }

  // 主内容feeds流
  Widget buildContentFeeds() => FliggyRefreshHeaderWidget(
    refreshController: _refreshController,
    onRefresh: () {
      Future.sync(
              () => _pagingController.refresh());
      return RefreshStatus.completed;
    },
    child: CustomScrollView(
        controller: _scrollController,
        physics: ClampingScrollPhysics(),
        slivers: <Widget>[
          if (!SafeAccess.isListEmpty(widget.tabInfo)) SliverPersistentHeader(
            pinned: true,
            delegate: _SliverAppBarDelegate(
                minHeight: 54,
                maxHeight: 54,
                child: CommunityTab(widget.tabInfo,tabId: _tabId, onTabClick: (data) {
                  // 解析tabID
                  _tabId.value = SafeAccess.safeParseInt(data['tabId'], def: _tabId.value);
                  _pagingController.refresh();
                  // 回到顶部
                  _scrollController.jumpTo(0);
                })
            ),
            floating: true,
          ),
          PagedSliverList<int, dynamic>.separated(
            pagingController: _pagingController,
            builderDelegate: PagedChildBuilderDelegate<dynamic>(
              animateTransitions: true,
              newPageErrorIndicatorBuilder: (context) => const SizedBox.shrink(),
              noItemsFoundIndicatorBuilder: (context) => const SizedBox.shrink(),
              noMoreItemsIndicatorBuilder: (context) => const SizedBox.shrink(),
              firstPageErrorIndicatorBuilder: (context) => const SizedBox.shrink(),
              // 可以替换骨架图
              firstPageProgressIndicatorBuilder:(context) => const SizedBox.shrink(),
              // 底部Loading
              newPageProgressIndicatorBuilder: (context) => const SizedBox.shrink(),
              itemBuilder: (context, item, index) {
                try {
                  Map itemData = item;
                  String type = itemData['cardType'] ?? '';
                  Map data = itemData['item'] ?? {};
                  String trackInfo = itemData['trackInfo'] ?? '';
                  Map<String, String> extTrackArgs = {
                    'tab_id': '$_tabId'
                  };
                  extTrackArgs['fpt'] = _pushTrack;
                  return CommunityFeedsFactory.createFeedItem(
                      type, index, data,
                      trackInfo: trackInfo,
                      extTrackArgs: extTrackArgs);

                } catch (e) {
                  print(e);
                }
                return SizedBox.shrink();
              },
            ), separatorBuilder: (BuildContext context, int index) {
            return Container(
              height: 6,
              color: Colors.transparent,
            );
          },
          ),
        ]
    ),
  );

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return VisibilityDetector(
      key: Key("community_find_page"),
      onVisibilityChanged: (info) {
        if (info.visibleFraction >= 1) {
          debugPrint('${widget.runtimeType}社区页可见度: ${info.visibleFraction}');
          // FfcUtils.onPageEnter(context, FfcTab.COMMUNITY_FIND);
        } else if (info.visibleFraction <= 0){
          debugPrint('${widget.runtimeType}社区页可见度: ${info.visibleFraction}');
          // 页面完全不可见
          if (widget.tracker != null) {
            widget.tracker.onPause().then((value) {
              // 上报完成后清理 SPM记录信息
              widget.tracker.refreshExposure();
            });
          }
        }
      },
      child: SafeArea(
        top: false,
        left: false,
        right: false,
        bottom: Platform.operatingSystem == "ohos" ? false : true,
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            // 背景
            SafeArea(
              child: Container(
                width: 375,
                height: 46,
                color: fd.color_white,
              ),
            ),
            // bg
            FRoundImage.base64(FfcDataUtils.findBgImage,
              width: 375,
              fit: BoxFit.fitWidth,
            ),
            SafeArea(
                bottom: false,
                child: Container(
                  margin: EdgeInsets.only(top: 46.0, bottom: Platform.isIOS ? 44.0 : 0.0),
                  child: buildContentFeeds(),
                )
            ),
            if (_tipBubbleMessage.isNotEmpty) SafeArea(
                bottom: false,
                child: Padding(
                  padding: const EdgeInsets.only(top: 46.0),
                  child: CommunityCommonBubble(_tipBubbleMessage),
                )
            ),
          ],
        ),
      ),
    );
  }

  // 加载缓存数据
  void _loadCache() {
    var _cache = FfcDataHelper.getStorageAssemble(FfcDataUtils.COMMUNITY_CACHE_KEY) ?? {};
    if (_cache is Map && !SafeAccess.isMapEmpty(_cache)) {
      _cacheData = _cache;
      _pagingController.itemList = SafeAccess.safeAccess<List>(_cacheData, "$_tabId", defaultValue: []);
    }
  }

  void _initParams() {
    if (widget.params.isEmpty) return;
    _contentId = widget.params['contentId'] ?? '';
    _pushTrack = widget.params['fpt'] ?? '';
    String ext = widget.params['ext'] ?? '';
    if (ext.isNotEmpty) {
      FfcUtils.trackCustom(context, "red_pot_content_push",
          params: {"ext": ext});
      _paging = json.decode(ext) ?? {};
    }
    // 优先级 默认页面带回的Tab ID > 接口带回的Tab ID
    if (widget.params.containsKey('tabId')) {
      _tabId.value = SafeAccess.safeParseInt(widget.params['tabId'], def: _tabId.value);
      // 使用一次后清除
      widget.params.remove("tabId");
    }else{
      _updateTabId();
    }
  }

  // 处理TabInfo中的TabID信息
  void _updateTabId() {
    _tabId.value = SafeAccess.safeParseInt(widget.tabInfo.firstWhere((element) => element['isSelected'] == true, orElse:()=> {})['tabId'],def: _tabId.value);
  }

  // 获取TabInfo 中已经选中的Index
  int get _getSelectedIndex {
    return widget.tabInfo.indexWhere((element) => element['isSelected'] == true);
  }

  // 数据请求
  Future<void> _fetchPage(int pageKey) async {
    Map params = {
      "pageSize": _pageSize,
      "pageNo": pageKey,
      "param": _paging,
      "destId": widget.params['destId']?.toString() ?? '',
      "contentId": _contentId,
      "clientType": "travel",
      "searchId": _searchId,
    };
    if (_tabId.value > 0) params['tabId'] = _tabId.value;
    final newModel = await ContentNet.requestCommunityFindPage(
        context, par: params)
        .timeout(Duration(seconds: 3), onTimeout: () {
      // FfcUtils.toast("网络超时");
      return new MtopResponseModel();
    }).catchError((error) {
      FfcUtils.toast("您查看的是缓存页面\n请恢复网络查看最新信息");
      return new MtopResponseModel();
    });

    // 解析
    if (!newModel.success) {
      _pagingController.error = newModel.errorMsg;
      return;
    }
    if (SafeAccess.safeParseBoolean(newModel.data['isOffline'])){
      FfcUtils.toast("您查看的是缓存页面\n请恢复网络查看最新信息", context: context);
    }
    // 内容id只消费一次
    _contentId = '';
    Map data = newModel.data?["data"];
    List feeds = data['list'] ?? [];
    _paging = data['paging'] ?? {};
    Map ext = data['ext'] ?? {};
    _searchId = _paging['searchId'] ?? '';
    // 第一页网络请求的特殊逻辑
    if (pageKey == 1) {
      if (ext.isNotEmpty) {
        safeSetState(() {
          _tipBubbleMessage = ext['tipBubbleMessage'] ?? '';
        });
      }
      // 缓存/并清除列表中显示的缓存
      _pagingController.itemList = [];
      Map cacheData = Map.from(_cacheData);
      cacheData["$_tabId"] = feeds;
      // 缓存feeds
      FfcDataHelper.setStorageAssemble<Map>(FfcDataUtils.COMMUNITY_CACHE_KEY, cacheData);
    }
    // 预加载
    _preloadContentPage(feeds);


    final isLastPage = !SafeAccess.safeParseBoolean(_paging['hasNext'], def: true);
    if (isLastPage) {
      _refreshController.loadNoData();
      _pagingController.appendLastPage(feeds);
    } else {
      _refreshController.resetNoData();
      final nextPageKey = pageKey + 1;
      _pagingController.appendPage(feeds, nextPageKey);
    }
  }

  void _preloadContentPage(List feeds) {
    Map<String, dynamic> params = {
      'list': feeds,
    };
    try {
      FFPerformance.sendParserRequest(params);
    } catch (e) {
      print(e);
    }
  }

}


class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  final double minHeight;
  final double maxHeight;
  final Widget child;

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => max(maxHeight, minHeight);

  @override
  Widget build(BuildContext context, double shrinkOffset, bool overlapsContent) {
    return SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}

