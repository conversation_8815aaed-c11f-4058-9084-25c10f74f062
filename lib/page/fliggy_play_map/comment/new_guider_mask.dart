import 'dart:io';
import 'dart:ui';

import 'package:flutter/material.dart';

import '../../../common/ffc_utils.dart';

/// <AUTHOR>
/// @date Created on 2025/2/11
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

class NewGuiderMask extends StatelessWidget {
  final List<String> imageUrls = [
    "https://gw.alicdn.com/imgextra/i2/O1CN01FGNFeP21EzGxbPGho_!!6000000006954-2-tps-750-968.png",
    "https://gw.alicdn.com/imgextra/i4/O1CN01v1nBih1zvvAfANIpU_!!6000000006777-2-tps-750-968.png",
    "https://gw.alicdn.com/imgextra/i2/O1CN01e5Vqrw28BVZmCzskL_!!6000000007894-2-tps-750-968.png",
  ];

  final VoidCallback finishCallback;

  NewGuiderMask({Key? key, required this.finishCallback}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    ValueNotifier<int> currentImageIndex = ValueNotifier<int>(0);
    return GestureDetector(
      onTap: () {
        FfcUtils.trackClickPlayMap(context,
            'guide.next_button_page_${currentImageIndex.value + 1}', 'guide',
            params: {});

        currentImageIndex.value++;
      },
      child: ValueListenableBuilder<int>(
          valueListenable: currentImageIndex,
          builder: (context, value, child) {
            if (value >= imageUrls.length) {
              finishCallback();
              return SizedBox.shrink();
            } else {
              FfcUtils.manualTrackExposurePlayMap(
                  'guide.close_button_page_${currentImageIndex.value + 1}', 'guide_page', {});
              FfcUtils.manualTrackExposurePlayMap(
                  'guide.next_button_page_${currentImageIndex.value + 1}', 'guide_page', {});
              return Container(
                width: 375,
                height: MediaQueryData.fromWindow(window).size.height,
                color: Color(0xFF000000).withOpacity(0.7),
                child: Column(
                  children: [
                    Expanded(
                      child: Stack(
                        children: [
                          Container(
                            alignment: Alignment.bottomCenter,
                            child: FRoundImage.network(
                              imageUrls[currentImageIndex.value],
                              fit: BoxFit.cover,
                              width: 375,
                              height: 484,
                              transitionAnimatedEnable: true,
                              placeholder: Container(
                                width: 375,
                                height: 484,
                                color: Color(0xFF000000).withOpacity(0.7),
                              ),
                            ),
                          ),
                          Align(
                            child: Container(
                              alignment: Alignment.bottomCenter,
                              padding: EdgeInsets.only(bottom: 115),
                              child: FRoundImage.network(
                                (value == 0 || value == 1)
                                    ? 'https://gw.alicdn.com/imgextra/i2/O1CN01PLnUv425yZ9WgXrPP_!!6000000007595-1-tps-200-200.gif'
                                    : 'https://gw.alicdn.com/imgextra/i2/O1CN01YPekyC1oZ3cipsix3_!!6000000005238-1-tps-200-200.gif',
                                fit: BoxFit.cover,
                                width: 100,
                                height: 100,
                                placeholder: Container(
                                  width: 375,
                                  height: 529,
                                  color: Color(0xFF000000).withOpacity(0.7),
                                ),
                                transitionAnimatedEnable: true,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // 关闭按钮
                    GestureDetector(
                      onTap: () {
                        finishCallback();
                        FfcUtils.trackClickPlayMap(
                            context,
                            'guide.close_button_page_${currentImageIndex.value + 1}',
                            'guide',
                            params: {});
                        currentImageIndex.value = 4;
                      },
                      child: Container(
                        alignment: Alignment.topCenter,
                        height: 40,
                        width: 100,
                        margin: EdgeInsets.only(bottom: 120),
                        child: Text(
                          '关闭(${(value + 1).toString()}/3)',
                          style: TextStyle(
                            color: Color(0xFFFFFFFF),
                            fontSize: 14,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              );
            }
          }),
    );
  }
}
