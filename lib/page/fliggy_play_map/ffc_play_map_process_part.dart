part of '../ffc_play_map_page.dart';

extension _PlayMapProcessPart on _FfcPlayMapPageState {
  // 地图创建完成逻辑
  void playMapCreated() {
    if (widget.params?['anchoring_id'] != null) {
      // 如果传了锚点卡片，则先移动到锚点卡片位置
      Map anchorMap = {};
      var anchoringCard = widget.params?['anchoring_id'];

      if (anchoringCard is Map) {
        anchorMap = anchoringCard;
      } else if (anchoringCard is String) {
        anchorMap = jsonDecode(anchoringCard);
      }

      // todo:这里要兼容选择其他类型
      LatLng? latlng = _getFirstLatLng(anchorMap);
      if (latlng != null) {
        _shouldRequestPlayInfo = true;
        _reloadPosition = true;
        _mapController.currentState?.updateCameraWithPoints(
            pointList: [CommonPointModel(latlng)],
            animated: false,
            duration: 50);
      }

    } else if (widget.params?['visible_id'] != null) {
      Map visibleMap = {};
      var visibleCard = widget.params?['visible_id'];

      if (visibleCard is Map) {
        visibleMap = visibleCard;
      } else if (visibleCard is String) {
        visibleMap = jsonDecode(visibleCard);
      }

      LatLng? latlng = _getFirstLatLng(visibleMap);

      if (latlng != null) {
        _shouldRequestPlayInfo = true;
        _reloadPosition = true;
        _mapController.currentState?.updateCameraWithPoints(
            pointList: [CommonPointModel(latlng)],
            animated: false,
            duration: 50);
      }
    } else {
      _initMapPosition();
    }
  }

  // 地图视角移动结束逻辑
  void playMapMovedEnd(
      CameraPosition? previousPosition,
      CameraPosition currentPosition,
      MapScreenRadius? radius,
      Function(bool) shouldHandleOverlap) {
    // 移动时先重置 POI 选中态
    // 重置地图不属于用户手动触发地图移动，只有用户手动触发地图移动才进行埋点
    if (!_reloadPosition &&
        _shouldRequestPlayInfo &&
        previousPosition != null) {
      cancelSelectedPOI();
    }

    // shouldHandleOverlap(true);
    // 如果previousPosition为空，则是初始化后首次回调，此时不进行请求
    // if (Platform.isIOS && previousPosition == null) return;
    // 如果回调前后cameraPosition一致，则认为没有移动
    debugPrint('muxian --- onCameraMoveEnd: ${currentPosition.zoom}');
    if (previousPosition == currentPosition) {
      if (_reloadPosition && _shouldRequestPlayInfo) {
        _requestPlayInfoHandler(currentPosition, radius);
      }
      return;
    }

    // 重置地图不属于用户手动触发地图移动，只有用户手动触发地图移动才进行埋点
    if (!_reloadPosition &&
        _shouldRequestPlayInfo &&
        previousPosition != null) {
      cancelSelectedPOI();

      String spmD = 'zoom_init';
      if (previousPosition.zoom < currentPosition.zoom) {
        spmD = 'zoom_in';
      } else if (currentPosition.zoom < previousPosition.zoom) {
        spmD = 'zoom_out';
      } else {
        spmD = 'zoom_move';
      }
      // 地图移动埋点
      FfcUtils.trackClickPlayMap(context, 'map.$spmD', 'map.$spmD');
    }
    if (_shouldRequestPlayInfo) {
      _requestPlayInfoHandler(currentPosition, radius);
    }
  }

  // 自定义地图样式
  Future<FliggyCustomStyleOptions> _mapCustomStyle() async {
    String platFormStr = 'Android';
    if (Platform.isIOS) {
      platFormStr = 'iOS';
    } else if (Platform.isAndroid) {
      platFormStr = 'Android';
    }

    String appVersion = await FfcDataHelper().getAppVersion();

    if (!FfcDataHelper().isVersionGreaterThan('9.10.16', appVersion)) {
      platFormStr = platFormStr + '_10';
    }

    ByteData styleByteData = await DefaultAssetBundle.of(context)
        .load('packages/$package/assets/play_map/$platFormStr/style.data');
    Uint8List styleData = styleByteData.buffer.asUint8List();

    ByteData styleExtraByteData = await DefaultAssetBundle.of(context).load(
        'packages/$package/assets/play_map/$platFormStr/style_extra.data');
    Uint8List styleExtraData = styleExtraByteData.buffer.asUint8List();
    FliggyCustomStyleOptions customStyle = FliggyCustomStyleOptions(true,
        styleData: styleData, styleExtraData: styleExtraData);
    return customStyle;
  }
}

// 预留的anchoring_card方案,我觉得很好,但是服务端嫌麻烦,后面再看吧
// if (widget.params?['anchoring_card'] != null) {
//       // 如果传了锚点卡片，则先移动到锚点卡片位置
//       Map anchorMap = {};
//       var anchoringCard = widget.params?['anchoring_card'];
//
//       if (anchoringCard is Map ) {
//         anchorMap = anchoringCard;
//       } else if (anchoringCard is String) {
//         anchorMap = jsonDecode(anchoringCard);
//       }
//
//       Map visibleCardMap = {};
//       var visibleCard = widget.params?['visible_card'];
//       if (visibleCard is Map ) {
//         visibleCardMap = visibleCard;
//       } else if (visibleCard is String) {
//         visibleCardMap = jsonDecode(visibleCard);
//       }
//       // 1.更新玩法数据
//       _updatePlayInfoData(anchorMap,showLevel: 4, isSelect: true);
//
//       _updatePlayInfoData(visibleCardMap,showLevel: 3);
//       // 2. 更新对应的point List
//       _updatePointData();
//       LatLng? latlng = _getFirstLatLng(anchorMap);
//       if (latlng!= null) {
//         // 首次加载,需要设置这两个标记
//         _shouldRequestPlayInfo = true;
//         // _reloadPosition = true;
//         _mapController.currentState?.movePositionCenter(latlng, duration: 50);
//       }
//       // 3. 刷新地图Marker
//       _mapController.currentState?.updateAllMarker();
//
//       // 补充请求常住地逻辑
//       _requestPermanent();
//     } else
