part of '../ffc_play_map_page.dart';


extension _PlayMapWidgetPart on _FfcPlayMapPageState {
  /// ============= widget build start ========
  Widget _buildUserLocationButton() {
    return Positioned(
      bottom:
      FfcDataHelper().is91018Padding ? (75 + MediaQuery.of(context).padding.bottom) : (Platform.isAndroid
          ? 23
          : (75 + MediaQuery.of(context).padding.bottom)),
      right: 15,
      child: GestureDetector(
        onTap: () async {
          // 点击埋点
          FfcUtils.trackClickPlayMap(context, 'map_button.location_button',
              'map_button.location_button');
          //重新获取用户当前定位
          _userLbs.value = await _getUserLbs();
          if (_userLbs.value == null) {
            FfcUtils.toast('定位权限未开启, 请到系统设置中开启', context: context);
          } else {
            _shouldRequestPlayInfo = true;
            _mapController.currentState?.movePositionCenter(_userLbs.value!, duration: 50);
          }
        },
        child: FfcExposeWidget(
            controllerName: 'map.location_button',
            spmAB: PlayMapConstant.spm_AB,
            spmCD: 'map_button.location_button',
            child: Container(
              width: 36,
              height: 36,
              decoration: BoxDecoration(
                  color: fd.color_white,
                  borderRadius: BorderRadius.circular(18),
                  boxShadow: const [
                    BoxShadow(
                        color: Colors.black26,
                        offset: Offset(2, 2),
                        blurRadius: 12,
                        spreadRadius: 1.2)
                  ]),
              child: Center(
                child: FRoundImage.network(
                  'https://gw.alicdn.com/imgextra/i3/O1CN01RKXtjj1q1k2Fkvgqr_!!6000000005436-2-tps-46-46.png',
                  width: 22.5,
                  height: 22.5,
                  fit: BoxFit.scaleDown,
                ),
              ),
            )),
      ),
    );
  }

  Widget _buildChatGroupButton() {
    return Positioned(
        bottom:
        FfcDataHelper().is91018Padding ? (75 + MediaQuery.of(context).padding.bottom) :
        Platform.isAndroid
            ? 23
            : (75 + MediaQuery.of(context).padding.bottom),
        left: 15,
        child: ValueListenableBuilder<ChatGroupModel?>(
          valueListenable: _chatGroupInfo,
          builder: (context, value, child) {
            bool visible = value != null;
            if (value != null) {
              _tempGroupInfo = value;
            }
            Map<String, String> trackParams = {};
            if (_chatGroupInfo.value?.groupId != null) {
              trackParams.safeAdd(
                  'fcid', _chatGroupInfo.value?.groupId.toString());
            }

            return GestureDetector(
              onTap: () {
                // 点击埋点
                FfcUtils.trackClickPlayMap(
                    context,
                    'map_chat.group_chat_card_${_tempGroupInfo?.groupId.toString()}',
                    'map.group_chat_card',
                    params: trackParams);
                // 前往群聊页面
                if (TextUtils.isNotEmpty(_tempGroupInfo?.url)) {
                  FfcUtils.goPage(context, false,
                      pageUrl: _tempGroupInfo?.url ?? '');
                }
              },
              child: AnimatedOpacity(
                opacity: visible ? 1 : 0,
                duration: Duration(milliseconds: 200),
                child: FfcExposeWidget(
                  controllerName: 'group_chat_card',
                  spmAB: PlayMapConstant.spm_AB,
                  spmCD:
                  'map_chat.group_chat_card_${_tempGroupInfo?.groupId.toString()}',
                  params: trackParams,
                  child: Container(
                    height: 36,
                    padding: EdgeInsets.only(right: 9),
                    decoration: BoxDecoration(
                        color: fd.color_white,
                        borderRadius: BorderRadius.circular(18),
                        border: Border.all(color: fd.color_white, width: 1.5),
                        boxShadow: [
                          BoxShadow(
                              color: desColor("#3A4F6A33").withOpacity(.2),
                              offset: Offset(0, 4),
                              blurRadius: 20,
                              spreadRadius: 0)
                        ]),
                    child: Row(
                      children: [
                        if (TextUtils.isNotEmpty(_tempGroupInfo?.logo))
                          ClipRRect(
                              borderRadius: BorderRadius.circular(18),
                              child: FRoundImage.network(
                                _tempGroupInfo?.logo ?? '',
                                width: 33,
                                height: 33,
                                fit: BoxFit.cover,
                              )),
                        if (TextUtils.isNotEmpty(_tempGroupInfo?.name))
                          Container(
                            constraints: BoxConstraints(maxWidth: 211),
                            padding: EdgeInsets.only(left: 3),
                            child: Text(
                              _tempGroupInfo?.name ?? '',
                              style: TextStyle(
                                  color: fd.color_darkgray,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          )
                      ],
                    ),
                  ),
                ),
                onEnd: () {
                  _tempGroupInfo = value;
                },
              ),
            );
          },
        ));
  }

  /// ============= widget build end ========
  /// ============= map start ========
  /// 构建地图
  Widget _buildMap() {
    return Listener(
      onPointerMove: (event) {
        _shouldRequestPlayInfo = true;
      },
      child: FliggyCommonMap(
        key: _mapController,
        initialCameraPosition: _initialCameraPosition(),
        cameraPadding:
        EdgeInsets.only(left: 20, right: 20, top: 140, bottom: 120),
        markerDataSource: () {
          return _markerDataSource;
        },
        customStyle: customstyle,
        createMarker: _createMarker,
        // drawMarkerCallback: _drawMarkerCallback,
        createCustomStyle: _mapCustomStyle,
        onMapCreated: playMapCreated,
        onCameraMoveEnd: playMapMovedEnd,
        // 关闭比例尺
        scaleEnabled: false,
      ),
    );
  }
}
