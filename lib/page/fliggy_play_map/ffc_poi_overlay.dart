import 'dart:io';
import 'package:fliggy_flutter_community/page/fliggy_play_map/utils/play_map_constant.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:fround_image/fround_image.dart';

import '../../common/ffc_utils.dart';
import 'model/play_map_model.dart';
import 'package:fliggy_flutter_map/fliggy_flutter_common_map.dart';
import 'package:fliggy_router/fliggy_router.dart';
import 'package:fliggy_usertrack/fliggy_usertrack.dart';

class POIPop extends StatelessWidget {

  POIPop(this.poiModel, this.cancelClick, this.lbsLat, this.lbsLng, this.isNewPage);

  final PlayInfoModel poiModel;

  String? lbsLat;

  String? lbsLng;

  bool isNewPage;

  /// 消失自己按钮
  VoidCallback? cancelClick;

  @override
  Widget build(BuildContext context) {

    // 卡片曝光
    FliggyUserTrackApi.getInstance().expose(context, PlayMapConstant.spm_AB + 'float_poi.poi_card_${poiModel.poiId}' , {});
    return Positioned(
      bottom: isNewPage ? 0 :
      FfcDataHelper().is91018Padding ? (46 + MediaQuery.of(context).padding.bottom) : (Platform.isAndroid
          ? 0
          : (46 + MediaQuery.of(context).padding.bottom))
      ,
      left: 0,
      right: 0, // 确保悬浮层在左右两侧有间距
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          FliggyUserTrackApi.getInstance().click(context, 'poi_card',PlayMapConstant.spm_AB + 'float_poi.poi_card_${poiModel.poiId}' , PlayMapConstant.spm_AB + 'float_poi.poi_card_${poiModel.poiId}', {});
          FliggyNavigatorApi.getInstance()
              .push(context, poiModel.tcPoiCardInfoDTO?.jumpUrl ?? '');
        },
        child: Container(
          width: double.infinity, // 宽度填满父组件
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter, // 渐变起始位置为底部中心
              end: Alignment.topCenter, // 渐变结束位置为顶部中心
              colors: [
                Color(0xFFFFFFFF), // 起始颜色：完全不透明的白色
                Color(0x00FFFFFF), // 结束颜色：全透明的白色
              ],
            ),
          ),
          padding: EdgeInsets.fromLTRB(12, 10, 12, isNewPage? 32 : 10),
          // 在中间放置真正的浮层容器
          child: Container(
            width: 350,
            padding: EdgeInsets.symmetric(horizontal: 9, vertical: 9),
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
                boxShadow: [
                  BoxShadow(
                    color: Color(0xFF000000).withOpacity(0.2), // 阴影颜色
                    spreadRadius: 0, // 阴影扩散半径
                    blurRadius: 48, // 模糊半径
                    offset: Offset(0, 9), // 阴影偏移量 (x, y)
                  ),
                ]),
            child: Row(
              children: [
                FRoundImage.network(
                  poiModel.tcPoiCardInfoDTO?.pic ?? '',
                  height: 132,
                  width: 111,
                  borderRadius: BorderRadius.circular(3),
                ),
                Container(
                  padding: EdgeInsets.only(left: 9),
                  constraints: BoxConstraints(maxWidth: 222, maxHeight: 132),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Expanded(
                        child: _buildContent(),
                      ),
                      _buildButtonList(context),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 主体信息
  Widget _buildContent() {
    return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // poi 名称
          Row(
            children: [
              Container(
                constraints: BoxConstraints(maxWidth: 180),
                child: Text(
                  poiModel.tcPoiCardInfoDTO?.title ?? '<未能获取>',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                      color: fd.color_darkgray,
                      fontSize: 15,
                      fontWeight: FontWeight.w600),
                ),
              ),
              Spacer(),
              GestureDetector(
                onTap: () {
                  if (cancelClick != null) {
                    cancelClick!();
                  }
                },
                child: Ficon(ICONS_GUANBI1, 17, Color(0x99000000)),
              )
            ],
          ),

          // 标签
          if (poiModel.tcPoiCardInfoDTO?.tagList != null)
            Padding(padding: EdgeInsets.only(top: 6), child: tagListWidget()),

          // 亮点信息
          if (poiModel.tcPoiCardInfoDTO?.highlightsDescription != null)
            Padding(
                padding: EdgeInsets.only(top: 6),
                child: Text(
                  poiModel.tcPoiCardInfoDTO?.highlightsDescription ?? '',
                  style: TextStyle(
                      color: Color(0xFF5C5F66),
                      fontWeight: FontWeight.w400,
                      fontSize: 12),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                )),

          // 榜单信息
          if (poiModel.tcPoiCardInfoDTO?.auctionHot != null)
            Padding(
                padding: EdgeInsets.only(top: 6),
                child: Row(
                  children: [
                    FRoundImage.network(
                      'https://gw.alicdn.com/imgextra/i2/O1CN01BTckHy1q4UFv9hopr_!!6000000005442-2-tps-84-84.png',
                      height: 14,
                      width: 14,
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 4),
                      child: Text(
                        poiModel.tcPoiCardInfoDTO?.auctionHot ?? '',
                        style: TextStyle(
                            color: Color(0xFF805540),
                            fontWeight: FontWeight.w400,
                            fontSize: 12),
                      ),
                    ),
                  ],
                ))
        ]
        // 地址
        );
  }

  Widget tagListWidget() {
    // 临时的 list
    List<TagList> tempList = [];
    final int totalNum = 16;
    int tempnum = 0;
    for (final tag in poiModel.tcPoiCardInfoDTO!.tagList!) {
      tempnum += tag.text?.length ?? 0;
      if (tempnum > totalNum) {
        break;
      }
      tempList.add(tag);
    }
    if (tempList.length > 0) {
      return Row(
        children: tempList.map((e) {
          return Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                color: Color(0xFFEBEBFF)),
            padding: EdgeInsets.only(left: 3, right: 3, top: 3,bottom: 2),
            margin: EdgeInsets.only(right: 6),
            child: Text(
              e.text ?? '',
              style: TextStyle(
                  fontSize: 10,
                  color: Color(0xFF6666FF),
                  fontWeight: FontWeight.w400,
                  height: 1.1),
            ),
          );
        }).toList(),
      );
    } else {
      return SizedBox.shrink();
    }
  }

  /// 按钮信息
  Widget _buildButtonList(BuildContext context) {
    if(poiModel.tcTaxiButtonDTO?.canUse ?? false) {
      FliggyUserTrackApi.getInstance().expose(context, PlayMapConstant.spm_AB + 'float_poi.taxi_button' , {});
    }
    // 导航按钮的埋点
    FliggyUserTrackApi.getInstance().expose(context, PlayMapConstant.spm_AB + 'float_poi.navigation_button' , {});
    return Container(
      alignment: Alignment.centerRight,
      child: Row(
        children: <Widget>[
          Spacer(),
          // 打车按钮
          if (poiModel.tcTaxiButtonDTO?.canUse ?? false)
            FutureBuilder<Map>(
                future: fetchData(),
                builder: (context, snapshot) {
                  return GestureDetector(
                    onTap: () {
                      FliggyUserTrackApi.getInstance().click(context, 'taxi_button',PlayMapConstant.spm_AB + 'float_poi.taxi_button' , PlayMapConstant.spm_AB + 'float_poi.taxi_button', {});
                      _taxiButtonClick(context);
                    },
                    child: Container(
                      constraints: const BoxConstraints(minWidth: 75),
                      decoration: BoxDecoration(
                          border:
                              Border.all(color: fd.color_auxiliary, width: 1),
                          borderRadius:
                              const BorderRadius.all(Radius.circular(15))),
                      height: 30,
                      // width: 75,
                      margin: const EdgeInsets.only(right: 8),
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      child: Center(
                        child: Row(
                          children: [Text(
                            '打车',
                            style: const TextStyle(
                                color: fd.color_midgray,
                                fontSize: 14,
                                fontWeight: FontWeight.w500),
                          ),
                  if (snapshot.hasData &&
                  snapshot.data!['hasPrice'] &&
                  snapshot.data!['price'] != null)

                    Container(
                      constraints: const BoxConstraints(maxWidth: 84),
                      child: Text(
                        ' 预估￥' + (snapshot.data!['price'] == null
                            ? ''
                            : snapshot.data!['price'].toString()) + '元',
                        style: const TextStyle(
                            color: fd.color_midgray,
                            fontSize: 11,
                            fontWeight: FontWeight.w500),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    )

                  ],
                        )
                        ,
                      ),
                    ),
                  );
                }),
          // 导航按钮
          GestureDetector(
            onTap: () {
              FliggyUserTrackApi.getInstance().click(context, 'navigation_button',PlayMapConstant.spm_AB + 'float_poi.navigation_button' , PlayMapConstant.spm_AB + 'float_poi.navigation_button', {});
              // 打开地图导航
              _poiListCustomButtonClick(context);
            },
            child: Container(
              decoration: BoxDecoration(
                  color: fd.color_brand_1,
                  borderRadius: BorderRadius.all(Radius.circular(30 / 2))),
              width: 75,
              height: 30,
              child: const Center(
                child: Text(
                  '导航',
                  style: TextStyle(
                      color: fd.color_darkgray,
                      fontSize: fd.font_size_text_2,
                      fontWeight: FontWeight.w500),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  // 导航按钮点击事件
  void _poiListCustomButtonClick(BuildContext context) async {
    List<NaviThirdMapType> availableThirdMap =
        await CommonMapNavigationManager.availableThirdMap();

    if (availableThirdMap.isEmpty) {
      if (Platform.isAndroid) {
        FBridgeApi.newInstance(context).toast('你未安装地图');
        return;
      }
    }
    // FBridgeApi.newInstance(context).toast(availableThirdMap.length.toString() + (poiModel.name ?? ''));
    showModalBottomSheet(
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(15.0),
            topRight: Radius.circular(15.0),
          ),
        ),
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return Padding(
            padding: EdgeInsets.only(bottom: isNewPage ? 0 : FfcDataHelper().is91018Padding ? 55 : Platform.isIOS ? 55 : 0),
            child: CommonMapNavigation(
              LatLng(poiModel.latitude!, poiModel.longitude!),
              availableThirdMap: availableThirdMap,
              poiName: poiModel.name,
              // itemClick: (text) {
              //   FliggyUserTrackApi.getInstance()
              //       .click(context, _pageName, kNaviMapChoosePrefixSpm + text, 'click', {});
              // },
            ),
          );
        });
  }

  /// 打车按钮点击事件
  void _taxiButtonClick(BuildContext context) {
    FliggyNavigatorApi.getInstance()
        .push(context, poiModel.tcTaxiButtonDTO?.jumpUrl ?? '');
  }

  // 打车价格事件
  Future<Map> fetchData() async {
    MtopRequestModel requestModel = MtopRequestModel.buildRequset(
        api: 'mtop.fliggy.content.community.content.city.wide.getTaxiPrice',
        version: '1.0',
        needLogin: true,
        params: {
          'id': poiModel.poiId ?? '',
          'cardType': 'poi',
          if (lbsLat!= null) 'latitude': lbsLat,// poiModel.latitude,
          if (lbsLat!= null) 'longitude': lbsLng,// poiModel.longitude,
        });
    MtopResponseModel responseModel =
        await FliggyMtopApi.getInstance().send(null, requestModel);

    return responseModel.data['data'];
  }
}
