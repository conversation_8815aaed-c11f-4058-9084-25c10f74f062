import 'dart:async';
import 'dart:typed_data';
import 'dart:ui';

import 'package:fliggy_flutter_community/common/collection_data_util.dart';
import 'package:fliggy_flutter_community/common/common_safe_access_util.dart';
import 'package:fliggy_flutter_community/common/text_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fliggy_flutter_map/fliggy_flutter_common_map.dart';
import 'package:fliggy_design/fliggy_design.dart';

import '../../../common/ffc_utils.dart';
import '../model/play_map_model.dart';

/// <AUTHOR>
/// @date Created on 2025/1/9
/// @email <EMAIL>
/// @company Alibaba Group
/// @description

class POIMarker extends FliggyMapMarker {
  final PlayInfoModel playModel;
  final bool tempMarker;

  ValueNotifier<bool> isSelect = ValueNotifier(false);

  POIMarker(this.playModel, this.tempMarker);

  @override
  FutureOr<Widget?> buildMarkerWidget() async {
    int playLogoW = 32;
    Uint8List? playLogoBytes;

    // 图片不能用网络图,只能用本地图,因为 marker 创建的时候不会等网络图回来,marker 只创建一次.如果一定要用,设置tempMarker为 true,让地图底层给刷新
    String cdnUrl = splicingCdnSuffix(
        'https://gw.alicdn.com/imgextra/i4/O1CN01trBUVK1k8iNPYCiXS_!!6000000004639-2-tps-112-112.png',
        widthInLogicPixel: playLogoW.toDouble());
    playLogoBytes = await CommonMarkerTools.convertImageUrlToBytes(cdnUrl,
        width: playLogoW * 3);

    int subImgH = 18;
    String? subTitle = _subTitleStr();

    Map<String, String> trackParams = {};
    if (playModel.chatGroupInfo?.groupId != null) {
      trackParams.safeAdd('fcid', playModel.chatGroupInfo?.groupId.toString());
    }
    return _buildContent(playLogoW, playLogoBytes, subImgH, subTitle);
  }

  Widget _buildContent(
      int playLogoW, Uint8List? playLogoBytes, int subImgH, String? subTitle) {

    // 备注:这里的问题是,屏幕下半部分的点击区向上偏移,基础线说解了,实际上还有问题,业务先兜一层
    return Container(
        height: 44,
        width: 176,
        color: Color(0x00FFFFFF),
    child: Row(
      children: [
        if (playLogoBytes != null)
          Image.memory(
            playLogoBytes,
            width: 32,
            height: 32,
            fit: BoxFit.cover,
            isAntiAlias: true,
          ),
        Container(
          constraints: BoxConstraints(maxWidth: 140),
          height: 32,
          color: Color(0x00FFFFFF),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                playModel.name ?? '',
                style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w600,
                    color: playModel.isSelected
                        ? Color(0xFF6666FF)
                        : fd.color_darkgray,
                    shadows: [
                      Shadow(offset: Offset(-1, -1), color: Color(0xFFFFFFFF)),
                    ],
                    height: 1.4),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              if (subTitle != null && subTitle.isNotEmpty)
                Text(
                  subTitle,
                  style: TextStyle(
                    fontSize: 11,
                    fontWeight: FontWeight.w400,
                    color: Color(0xFF6666FF),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
            ],
          ),
        )
      ],
    ));
  }

  String? _subTitleStr() {
    if (playModel.subTitles != null && playModel.subTitles!.isNotEmpty) {
      Map subTitleMap = playModel.subTitles?.first ?? {};
      String subTitleStr =
          CommonSafeAccess.safeParseString(subTitleMap['title']) ?? '';
      return subTitleStr;
    }
    return null;
  }

  @override
  Duration renderDelay() {
    return Duration(milliseconds: tempMarker ? 5 : 20);
  }
}
