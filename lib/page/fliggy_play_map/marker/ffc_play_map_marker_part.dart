part of '../../ffc_play_map_page.dart';


/// 地图主页面的marker部分
extension _PlayMapMarkerPart on _FfcPlayMapPageState {
  /// 用户lbs对应的point
  CommonPointModel? get _userPoint {
    double? lat = _userLbs.value?.latitude;
    double? lng = _userLbs.value?.longitude;
    if (lat == null || lng == null) return null;
    CommonPointModel userPoint = CommonPointModel(LatLng(lat, lng));
    if (userPoint.isValid()) {
      // 先特判一下，只有安卓才缓存 userlbs marker
      if (Platform.isAndroid) {
        userPoint.pointBizId =
        '${_userInfoModel?.avatar} - ${_userLbs.value.toString()} - $_lbsCityId';
      }
      return userPoint;
    }
    return null;
  }

  /// 常住地对应的point
  CommonPointModel? get _permanentPoint {
    double? lat = _permanentCity?.latitude;
    double? lng = _permanentCity?.longitude;
    if (lat == null || lng == null) return null;
    CommonPointModel permanentPoint = CommonPointModel(LatLng(lat, lng));
    if (permanentPoint.isValid()) {
      return permanentPoint;
    }
    return null;
  }

  /// ============= marker start ========
  FutureOr<CommonFliggyMarker?> _createMarker(
      String markerTypeStr, CommonPointModel point, bool tempMarker) async {
    PlayMarkerType markerType =
    PlayMapMarkerFactory.markerStrToType(markerTypeStr);
    CommonFliggyMarker? marker;
    if (markerType == PlayMarkerType.userLBS) {
      marker = await _createUserLBSMarker(markerType, point, tempMarker);
    }
    if (markerType == PlayMarkerType.card) {
      marker = await _createPlayCardMarker(
          markerType, markerTypeStr, point, tempMarker);
    }
    if (markerType == PlayMarkerType.poiCards) {
      marker = await _createPOICardMarker(
          markerType, markerTypeStr, point, tempMarker);
    }
    return marker;
  }

  /// 创建用户lbs marker
  Future<CommonFliggyMarker?> _createUserLBSMarker(PlayMarkerType markerType,
      CommonPointModel point, bool tempMarker) async {
    int markerStart = DateTime.now().millisecondsSinceEpoch;
    CommonFliggyMarker? marker;
    if (_userInfoModel == null) return marker;

    String spmCD = 'map_user.usericon';
    String trackName = 'map.usericon';
    marker = await PlayMapMarkerFactory.createMarker(
        context, markerType, point.latLng,
        data: {
          'userInfo': _userInfoModel,
          'userLBS': _userLbs.value,
          'userCityID': _lbsCityId
        },
        zIndex: 0, onDisplay: () {
      Map<String, String> trackParams = {};
      trackParams.safeAdd('lbs', _userLbs.value?.toString() ?? '');
      trackParams.safeAdd('city_id', _lbsCityId?.toString() ?? '');
      FfcUtils.manualTrackExposurePlayMap(spmCD, trackName, trackParams,
          mixString: _lbsCityId.toNoNullStr);
    }, onTap: (_) {
      Map<String, String> trackParams = {};
      trackParams.safeAdd('lbs', _userLbs.value?.toString() ?? '');
      trackParams.safeAdd('city_id', _lbsCityId?.toString() ?? '');
      // 点击tabBar用户头像
      FfcUtils.trackClickPlayMap(context, spmCD, trackName,
          params: trackParams);
      _userIconClicked();
    });
    int markerEnd = DateTime.now().millisecondsSinceEpoch;
    debugPrint(
        'muxian --- marker time: ${point.pointBizId} - ${markerEnd - markerStart}');
    return marker;
  }

  /// 创建单玩法 marker
  Future<CommonFliggyMarker?> _createPlayCardMarker(PlayMarkerType markerType,
      String markerTypeStr, CommonPointModel point, bool tempMarker) async {
    int markerStart = DateTime.now().millisecondsSinceEpoch;
    CommonFliggyMarker? marker;
    PlayInfoModel playModel = _findPlayModel(markerTypeStr, point);
    if (playModel.bizId == null || TextUtils.isEmpty(playModel.name))
      return marker;

    String spmCD = 'map_card.route_card_${playModel.contentId}';
    String trackName = 'single_play_marker';
    Map<String, String> trackParams = {};
    if (playModel.chatGroupInfo?.groupId != null) {
      trackParams.safeAdd('fcid', playModel.chatGroupInfo?.groupId.toString());
    }
    marker = await PlayMapMarkerFactory.createMarker(
        context, markerType, point.latLng,
        data: {'playModel': playModel, 'tempMarker': tempMarker},
        zIndex: playModel.showLevel, onDisplay: () {
      FfcUtils.manualTrackExposurePlayMap(spmCD, trackName, trackParams);
      // 增加任意卡片曝光上报spm
      // FfcUtils.manualTrackExposurePlayMap('map_card.route_card_$markerTypeStr',
      //     trackName, {'contentId': playModel.contentId});
      FfcUtils.manualTrackExposurePlayMap('map_card.route_card',
          trackName, {});
    }, onTap: (_) {
      // 点击埋点
      FfcUtils.trackClickPlayMap(context, spmCD, trackName,
          params: trackParams);
      FfcUtils.trackClickPlayMap(context, 'map_card.route_card', trackName,
          params: trackParams);
      // 跳转玩法详情
      if (TextUtils.isNotEmpty(playModel.detailUrl)) {
        FfcUtils.goPage(context, false, pageUrl: playModel.detailUrl ?? '');
      }
    }, onOverLapCheck: (overLapMap) {
      String BizId = point.pointBizId ?? '';
      debugPrint('muxian --- BizId: $BizId --- overLapMap: $overLapMap');
      return overLapMap.containsKey(MarkerTypeCards) ? false : true;
    });
    marker?.overLapScale = 0.6;
    int markerEnd = DateTime.now().millisecondsSinceEpoch;
    debugPrint(
        'muxian --- marker time: ${point.pointBizId} - ${markerEnd - markerStart}');
    return marker;
  }

  /// 创建POI卡片 marker
  Future<CommonFliggyMarker?> _createPOICardMarker(PlayMarkerType markerType,
      String markerTypeStr, CommonPointModel point, bool tempMarker) async {
    int markerStart = DateTime.now().millisecondsSinceEpoch;
    CommonFliggyMarker? marker;
    PlayInfoModel playModel = _findPlayModel(markerTypeStr, point);
    if (playModel.poiId == null && TextUtils.isEmpty(playModel.name))
      return marker;

    String spmCD = 'map_card.poi_name_${playModel.poiId}';
    String trackName = 'single_play_marker';
    Map<String, String> trackParams = {};
    if (playModel.chatGroupInfo?.groupId != null) {
      trackParams.safeAdd('fcid', playModel.chatGroupInfo?.groupId.toString());
    }
    marker = await PlayMapMarkerFactory.createMarker(
        context, markerType, point.latLng,
        data: {'playModel': playModel, 'tempMarker': tempMarker},
        zIndex: 1, onDisplay: () {
      FfcUtils.manualTrackExposurePlayMap(spmCD, trackName, trackParams);
      FfcUtils.manualTrackExposurePlayMap('map_card.poi_name',
          trackName, {});
    }, onTap: (_) {
      // 这个地方主要是如果反复点,会不通知刷新,所以先置一次 false,实际体验中页面不会有很大的闪烁
      _showPOIPop.value = false;
      // 点击埋点
      FfcUtils.trackClickPlayMap(context, spmCD, trackName,
          params: trackParams);
      FfcUtils.trackClickPlayMap(context, 'map_card.poi_name', trackName,
          params: trackParams);
      // 1.更新玩法数据
      List<PlayInfoModel>? infoList = _playInfoMap[markerTypeStr];
      // 找到 poiId 相同的,进行更新
      for (PlayInfoModel model in infoList ?? []) {
        if (model.poiId == playModel.poiId) {
          model.isSelected = true;
        } else {
          model.isSelected = false;
        }
      }

      // 2. 更新对应的point List
      _updatePointData();

      // 4. 刷新地图Marker
      _mapController.currentState?.updateAllMarker();

      selectPOIInfo = playModel;
      _showPOIPop.value = true;
    }, onOverLapCheck: (overLapMap) {
      String BizId = point.pointBizId ?? '';
      debugPrint('muxian --- BizId: $BizId --- overLapMap: $overLapMap');
      // poi卡片当与玩法卡片或其他poi碰撞时都进行隐藏
      if (overLapMap.containsKey(MarkerTypeCards) ||
          overLapMap.containsKey(MarkerTypePOI)) {
        return false;
      }
      return true;
    });
    // 碰撞比例
    marker?.overLapScale = 0.6;
    int markerEnd = DateTime.now().millisecondsSinceEpoch;
    debugPrint(
        'muxian --- marker time: ${point.pointBizId} - ${markerEnd - markerStart}');
    return marker;
  }

/// ============= marker end ========
}