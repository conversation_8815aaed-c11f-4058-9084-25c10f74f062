import 'dart:async';

import 'package:fliggy_flutter_community/model/ffc_user_info.dart';
import 'package:fliggy_flutter_community/page/fliggy_play_map/marker/single_play_marker.dart';
import 'package:fliggy_flutter_community/page/fliggy_play_map/marker/user_lbs_marker.dart';
import 'package:flutter/cupertino.dart';
import 'package:fliggy_flutter_map/fliggy_flutter_common_map.dart';
import '../../ffc_play_map_page.dart';
import '../model/play_map_model.dart';
import 'POI_marker.dart';

const String MarkerTypeUser = 'userLBS';
const String MarkerTypeCards = 'playCards';
const String MarkerTypeCity = 'playCity';
/// POI 类型卡片
const String MarkerTypePOI = 'poiCards';

enum PlayMarkerType {
  unknown,
  userLBS, // 用户lbs
  card, // 单玩法
  city, // 聚合玩法
  poiCards
}

class PlayMapMarkerFactory {
  /// type enum to string
  static String markerTypeToStr(PlayMarkerType type) {
    switch(type) {
      case PlayMarkerType.card:
        return MarkerTypeCards;
      case PlayMarkerType.city:
        return MarkerTypeCity;
      default:
        return '';
    }
  }

  /// type string to enum
  static PlayMarkerType markerStrToType(String typeStr) {
    switch(typeStr) {
      case MarkerTypeUser:
        return PlayMarkerType.userLBS;
      case MarkerTypeCards:
        return PlayMarkerType.card;
      case MarkerTypeCity:
        return PlayMarkerType.city;
      case MarkerTypePOI:
        return PlayMarkerType.poiCards;
      default:
        return PlayMarkerType.unknown;
    }
  }

  static FutureOr<CommonFliggyMarker?> createMarker(BuildContext context, PlayMarkerType type, LatLng position, {
    Map? data,
    ArgumentCallback<String>? onTap,
    double? zIndex,
    Function()? onDisplay,
    Function(Map<String, List<CommonMarkPosition>>)? onOverLapCheck}) async {
    double ratio = 3.0;
    FliggyMapMarker? markerRender;

    switch (type) {
      case PlayMarkerType.userLBS:
        markerRender = _createUserLBSMarker(data);
        break;
      case PlayMarkerType.card:
        markerRender = _createSinglePlayMarker(data);
        break;
      case PlayMarkerType.poiCards:
        markerRender = _createPOIMarker(data);
        break;
      default:
        break;
    }
    if(markerRender == null) return null;

    markerRender.setRatio(ratio);
    BitmapDescriptor icon = await markerRender.getBytesFromCanvas();
    CommonFliggyMarker mapMarker = CommonFliggyMarker(
      mapMarker: markerRender,
      customAnchor: true,
      position: position,
      icon: icon,
      infoWindowEnable: false,
      zIndex: zIndex ?? 0,
      onTap: onTap,
      onDisplay: onDisplay,
      onOverLapCheck: onOverLapCheck,
      anchor: type == PlayMarkerType.poiCards ? const Offset(0, 0.5) : const Offset(0.5, 1.0),
    );
    return mapMarker;
  }

  static FliggyMapMarker? _createSinglePlayMarker(Map? data) {
    PlayInfoModel? playModel = data?['playModel'];
    bool tempMarker = data?['tempMarker'] ?? false;
    if(playModel == null) return null;

    FliggyMapMarker? marker = PlayCardMarker(playModel, tempMarker);
    return marker;
  }

  static FliggyMapMarker? _createUserLBSMarker(Map? data) {
    FFCUserInfoModel useInfo = data?['userInfo'];
    LatLng? userLbS = data?['userLBS'];
    num? userCityID = data?['userCityID'];
    FliggyMapMarker? marker = UserLBSMarker(useInfo, userLbS: userLbS, userCityID: userCityID);
    return marker;
  }

  /// POI 卡片 marker
  static FliggyMapMarker? _createPOIMarker(Map? data) {
    PlayInfoModel? playModel = data?['playModel'];
    // bool tempMarker = data?['tempMarker'] ?? false;
    if(playModel == null) return null;

    FliggyMapMarker? marker = POIMarker(playModel, false);
    return marker;
  }
}