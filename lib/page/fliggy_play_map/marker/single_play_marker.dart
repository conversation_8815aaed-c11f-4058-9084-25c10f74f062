import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'dart:typed_data';
import 'dart:ui';

import 'package:fliggy_flutter_community/common/collection_data_util.dart';
import 'package:fliggy_flutter_community/common/common_safe_access_util.dart';
import 'package:fliggy_flutter_community/common/text_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fliggy_flutter_map/fliggy_flutter_common_map.dart';
import 'package:fliggy_design/fliggy_design.dart';

import '../../../common/ffc_utils.dart';
import '../model/play_map_model.dart';
import '../utils/play_map_constant.dart';

class PlayCardMarker extends FliggyMapMarker {
  final PlayInfoModel playModel;
  final bool tempMarker;

  PlayCardMarker(this.playModel, this.tempMarker);

  @override
  FutureOr<Widget?> buildMarkerWidget() async {
    int playLogoW = 36;
    Uint8List? playLogoBytes;
    if (tempMarker) {
      playLogoBytes =
          base64Decode(PlayMapConstant.playPlaceHolder.split(',')[1]);
    } else if (TextUtils.isNotEmpty(playModel.logo) && !tempMarker) {
      String cdnUrl = splicingCdnSuffix(playModel.logo!,
          widthInLogicPixel: playLogoW.toDouble());
      playLogoBytes = await CommonMarkerTools.convertImageUrlToBytes(cdnUrl,
          width: playLogoW * 3);
    }

    int subImgH = 18;
    String subTitle = _subTitleStr();

    Map<String, String> trackParams = {};
    if (playModel.chatGroupInfo?.groupId != null) {
      trackParams.safeAdd('fcid', playModel.chatGroupInfo?.groupId.toString());
    }
    return CommonBubble(
        shadow: true,
        margin: const BubbleEdges.only(top: 4, left: 4, bottom: 1, right: 2),
        nip: BubbleNip.centerBottom,
        nipWidth: 12,
        nipHeight: 8,
        color: playModel.isSelected ? Color(0xFFFFF7CC) : Colors.white,
        padding: BubbleEdges.zero,
        borderWidth: 2,
        child: _buildContent(playLogoW, playLogoBytes, subImgH, subTitle));
  }

  Widget _buildContent(
      int playLogoW, Uint8List? playLogoBytes, int subImgH, String subTitle) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          padding: EdgeInsets.only(top: 6, bottom: 6, left: 3, right: 3),
          constraints: BoxConstraints(minHeight: 47),
          decoration: BoxDecoration(
              // color: Colors.green,
              borderRadius: BorderRadius.all(Radius.circular(6.0))),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题 + 子标题
              Padding(
                padding: EdgeInsets.only(left: 35),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      child: Text(playModel.name ?? '',
                        style: TextStyle(
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                            color: fd.color_darkgray,
                          height: 1.4
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      constraints: BoxConstraints(
                        maxWidth: 88
                      ),
                      // color: Colors.yellow,
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if(subTitle.isNotEmpty)
                          Container(
                            margin: EdgeInsets.only(top: 1.5),
                            padding: EdgeInsets.symmetric(horizontal: 3),
                            decoration: BoxDecoration(
                              color: fd.color_indigo_5,
                              borderRadius: BorderRadius.all(Radius.circular(2))
                            ),
                            child: Text(subTitle,
                              style: TextStyle(
                                fontSize: 10.5,
                                fontWeight: playModel.subTitles?.first['type'] == 'tag' ? FontWeight.w400 : FontWeight.w700,
                                color: fd.color_indigo_1,
                                fontFamily: playModel.subTitles?.first['type'] == 'tag' ? 'PingFang SC' : 'FliggyFont',
                              ),
                            ),
                          )
                      ],
                    )
                    // if(subImgBytes != null)
                    //   Image.memory(subImgBytes, height: subImgH.toDouble())
                  ],
                ),
              ),
              // 群聊信息
              _buildChatGroup()
            ],
          ),
        ),
        if (playLogoBytes != null) _buildPlayLogo(playLogoBytes),
      ],
    );
  }

  Widget _buildPlayLogo(Uint8List playLogoBytes) {
    return Positioned(
      top: -4,
      left: -2,
      child: Transform.rotate(
        angle: -(8 * (pi.toDouble() / 180)),
        child: Container(
          clipBehavior: Clip.antiAlias,
          width: 33,
          height: 45,
          decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(Radius.circular(2)),
              boxShadow: [
                BoxShadow(
                    color: Colors.black.withOpacity(.1),
                    offset: Offset(0, 4),
                    blurRadius: 6,
                    spreadRadius: 0)
              ]),
          child: Center(
            child: ClipRect(
              clipBehavior: Clip.antiAlias,
              child: Image.memory(
                playLogoBytes,
                width: 29,
                height: 41,
                fit: BoxFit.cover,
                isAntiAlias: true,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildChatGroup() {
    if (TextUtils.isEmpty(playModel.chatGroupInfo?.name))
      return SizedBox.shrink();
    // 'playModel.chatGroupInfo?.name ?? '''
    return Container(
      margin: EdgeInsets.only(top: 7),
      padding: EdgeInsets.all(3),
      decoration: BoxDecoration(
          color: fd.color_indigo_5,
          borderRadius: BorderRadius.all(Radius.circular(4))),
      constraints: BoxConstraints(
        maxWidth: 128,
      ),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Text(
          playModel.chatGroupInfo?.name ?? '',
          style: TextStyle(
              fontWeight: FontWeight.w400,
              fontSize: 11,
              color: fd.color_midgray),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    );
  }

  String _subTitleStr() {
    Map subTitleMap = playModel.subTitles?.first ?? {};
    String subTitleStr =
        CommonSafeAccess.safeParseString(subTitleMap['title']) ?? '';
    return subTitleStr;
  }

  @override
  Duration renderDelay() {
    return Duration(milliseconds: tempMarker ? 5 : 20);
  }
}
