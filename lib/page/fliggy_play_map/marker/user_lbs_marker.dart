import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:fliggy_flutter_map/fliggy_flutter_common_map.dart';

import '../../../common/ffc_utils.dart';
import '../../../model/ffc_user_info.dart';

class UserLBSMarker extends FliggyMapMarker {

  final FFCUserInfoModel userInfo;
  final LatLng? userLbS;
  final num? userCityID;
  UserLBSMarker(this.userInfo, {
    this.userLbS,
    this.userCityID});

  @override
  FutureOr<Widget?> buildMarkerWidget() async {
    int bgW = 48;
    int avatarW = 26;

    bool hasLogin = await FfcUtils.hasLogin();
    Uint8List? avatarBytes;
    if(hasLogin) {
      String userAvatar = userInfo.avatar ?? '';
      String cdnUrl = splicingCdnSuffix(userAvatar, widthInLogicPixel: avatarW.toDouble());
      avatarBytes = await CommonMarkerTools.convertImageUrlToBytes(cdnUrl, width: avatarW * 2);
    } else {
      avatarBytes = base64Decode(FfcDataUtils.loginIcon.split(',')[1]);
    }
    if(avatarBytes == null) return null;

    return Container(
      width: bgW.toDouble(),
      height: bgW.toDouble(),
      decoration: BoxDecoration(
        color: desColor('#6666FF').withOpacity(.3),
        borderRadius: BorderRadius.circular(31)
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 4),
            child: Ficon(ICONS_WEIZHI_MIAN, 41, Colors.white),
          ),
          ClipRRect(
            borderRadius: BorderRadius.circular(18),
            child: Image.memory(
                avatarBytes,
                width: avatarW.toDouble(),
                height: avatarW.toDouble(),
                fit: BoxFit.cover),
          )
        ],
      ),
    );
  }

  @override
  Duration renderDelay() {
    return Duration(milliseconds: 50);
  }
}