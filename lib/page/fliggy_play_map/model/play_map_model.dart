import 'package:fliggy_flutter_community/common/ffc_utils.dart';
import 'package:fliggy_flutter_community/common/common_safe_access_util.dart';

import '../utils/play_map_constant.dart';

class PlayMapTabModel {
  /// tabId
  num? tabId;
  /// tab名称
  String? tabName;
  /// tab类型
  String? tabType;
  /// tab index
  int? indexValue;
  /// tab等级
  int? level;
  /// 地点信息列表
  List<PlayMapTabModel>? children;

  PlayMapTabModel({
    this.tabId,
    this.tabName,
    this.tabType,
    this.indexValue,
    this.level,
    this.children});

  PlayMapTabModel.fromJson(Map json) {
    tabId = CommonSafeAccess.safeParseNum(json['tabId']);
    tabName = CommonSafeAccess.safeParseString(json['tabName']);
    tabType = CommonSafeAccess.safeParseString(json['tabType']);
    indexValue = CommonSafeAccess.safeParseInt(json['indexValue']);
    level = CommonSafeAccess.safeParseInt(json['level']);
    if (json['children'] != null) {
      children = <PlayMapTabModel>[];
      json['children'].forEach((v) {
        children!.add(PlayMapTabModel.fromJson(v));
      });
    }
  }
}

/// 城市信息Model
class CityModel {
  /// 城市ID
  String? cityId;
  /// 城市名称
  String? cityName;
  /// 经度
  double? longitude;
  /// 纬度
  double? latitude;

  CityModel({
    this.cityId,
    this.cityName,
    this.longitude,
    this.latitude});

  CityModel.fromJson(Map json) {
    cityId = CommonSafeAccess.safeParseString(json['cityId']);
    cityName = CommonSafeAccess.safeParseString(json['cityName']);
    longitude = CommonSafeAccess.safeParseDouble(json['longitude']);
    latitude = CommonSafeAccess.safeParseDouble(json['latitude']);
  }
}

/// 玩法信息Model
class PlayInfoModel {
  /// 玩法内容ID
  String? contentId;
  /// 玩法名称
  String? name;
  /// 详情URL
  String? detailUrl;
  /// 玩法头图
  String? logo;
  /// 纬度
  double? latitude;
  /// 经度
  double? longitude;
  /// 玩法类型
  String? playLevel;
  /// 玩法副标题
  List<Map>? subTitles;
  /// 群聊信息
  ChatGroupModel? chatGroupInfo;

  /// 展示等级,默认是 2,如果是上次路由带入,是 3
  double showLevel = 2;

  /// 备注一下,这个地方以前就在一块,新增模型成本很高,后续有机会拆开不同的模型方便扩展
  /// poi 卡片-poiId
  String? poiId;

  /// 是否选中
  bool isSelected = false;

  /// tabId,因为卡片需要以 tab 为单位进行缓存,增加了一个标识
  String tabId = '';

  /// poi 卡片详情的信息
  TcPoiCardInfoDTO? tcPoiCardInfoDTO;
  TcTaxiButtonDTO? tcTaxiButtonDTO;
  // /// poi 卡片-poi 名称
  // String? name;

  /// MARKER 的标识,相同 bizid 的 marker 不会重绘,即使数据改变
  String? get bizId {
    String bizId = '';
    if(contentId != null) {
      bizId = '$bizId ${contentId.toString()}';
    }
    if (poiId != null) {
      bizId = '$bizId ${poiId.toString()}';
    }
    // if(name != null) {
    //   bizId = '$bizId + $name';
    // }

    bizId = '$bizId+${isSelected.toString()}+$tabId';
    return bizId;
  }

  // String get subTitleImg {
  //   switch(playLevel) {
  //     case 'INCREDIBLE':
  //       return PlayMapConstant.play_subTitle_INCREDIBLE;
  //     case 'MUST_VISIT':
  //       return PlayMapConstant.play_subTitle_MUST_VISIT;
  //     case 'WORTH_TRYING':
  //       return PlayMapConstant.play_subTitle_WORTH_TRYING;
  //     case 'CASUAL':
  //       return PlayMapConstant.play_subTitle_CASUAL;
  //   }
  //   return '';
  // }

  PlayInfoModel({
    this.contentId,
    this.name,
    this.detailUrl,
    this.logo,
    this.latitude,
    this.longitude,
    this.playLevel,
    this.subTitles,
    this.chatGroupInfo,
    this.poiId,
    this.tcPoiCardInfoDTO,
    this.tcTaxiButtonDTO,
    this.isSelected = false,
  });

  PlayInfoModel.fromJson(Map json) {
    contentId = CommonSafeAccess.safeParseString(json['contentId']);
    name = CommonSafeAccess.safeParseString(json['name']);
    detailUrl = CommonSafeAccess.safeParseString(json['detailUrl']);
    logo = CommonSafeAccess.safeParseString(json['logo']);
    latitude = CommonSafeAccess.safeParseDouble(json['latitude']);
    longitude = CommonSafeAccess.safeParseDouble(json['longitude']);
    playLevel = CommonSafeAccess.safeParseString(json['playLevel']);
    poiId = CommonSafeAccess.safeParseString(json['poiId']);
    if(json['subTitles'] != null) {
      subTitles = <Map>[];
      json['subTitles'].forEach((v) {
        subTitles?.add(CommonSafeAccess.safeParseMap(v));
      });
    }
    Map chatGroup = SafeAccess.safeParseMap(json['chatGroupInfo']);
    if(chatGroup.isNotEmpty) {
      chatGroupInfo = ChatGroupModel.fromJson(chatGroup);
    }

    tcPoiCardInfoDTO = json['tcPoiCardInfoDTO'] != null
        ? new TcPoiCardInfoDTO.fromJson(json['tcPoiCardInfoDTO'])
        : null;
    tcTaxiButtonDTO = json['tcTaxiButtonDTO'] != null
        ? new TcTaxiButtonDTO.fromJson(json['tcTaxiButtonDTO'])
        : null;
    isSelected = CommonSafeAccess.safeParseBoolean(json['isSelected']) ?? false;
    if (isSelected) {
      if (json['showLevel']!= null) {
        showLevel = CommonSafeAccess.safeParseDouble(json['showLevel']) ?? 4;
      } else {
        showLevel = 4;
      }

    }
  }
}

/// 群聊信息Model
class ChatGroupModel {
  /// 群聊ID
  num? groupId;
  /// 群聊名称
  String? name;
  /// 群聊logo
  String? logo;
  /// 群聊链接
  String? url;

  ChatGroupModel({
    this.groupId,
    this.name,
    this.logo,
    this.url
  });

  ChatGroupModel.fromJson(Map json) {
    groupId = CommonSafeAccess.safeParseNum(json['groupId']);
    logo = CommonSafeAccess.safeParseString(json['logo']);
    name = CommonSafeAccess.safeParseString(json['name']);
    url = CommonSafeAccess.safeParseString(json['url']);
  }
}


/// poi 卡片详情信息
class TcPoiCardInfoDTO {
  String? address;
  String? auctionHot;
  String? pic;
  String? highlightsDescription;
  RecommendStruct? recommendStruct;
  List<TagList>? tagList;
  String? title;

  String? jumpUrl;

  TcPoiCardInfoDTO(
      {this.address,
        this.auctionHot,
        this.pic,
        this.highlightsDescription,
        this.recommendStruct,
        this.tagList,
        this.title,
        this.jumpUrl
      });

  TcPoiCardInfoDTO.fromJson(Map<String, dynamic> json) {
    address = json['address'];
    auctionHot = json['auctionHot'];
    pic = json['pic'];
    highlightsDescription = json['highlightsDescription'];
    recommendStruct = json['recommendStruct'] != null
        ? new RecommendStruct.fromJson(json['recommendStruct'])
        : null;
    if (json['tagList'] != null) {
      tagList = <TagList>[];
      json['tagList'].forEach((v) {
        tagList!.add(new TagList.fromJson(v));
      });
    }
    title = json['title'];

    jumpUrl = json['jumpUrl']?['url'] ?? '';
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['address'] = this.address;
    data['auctionHot'] = this.auctionHot;
    data['pic'] = this.pic;
    if (this.recommendStruct != null) {
      data['recommendStruct'] = this.recommendStruct!.toJson();
    }
    if (this.tagList != null) {
      data['tagList'] = this.tagList!.map((v) => v.toJson()).toList();
    }
    data['title'] = this.title;
    return data;
  }
}

class RecommendStruct {
  String? commentDesc;
  String? recommendCount;
  String? recommendDesc;
  String? score;

  RecommendStruct(
      {this.commentDesc, this.recommendCount, this.recommendDesc, this.score});

  RecommendStruct.fromJson(Map<String, dynamic> json) {
    commentDesc = json['commentDesc'];
    recommendCount = json['recommendCount'];
    recommendDesc = json['recommendDesc'];
    score = json['score'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['commentDesc'] = this.commentDesc;
    data['recommendCount'] = this.recommendCount;
    data['recommendDesc'] = this.recommendDesc;
    data['score'] = this.score;
    return data;
  }
}

class TagList {
  String? text;

  TagList({this.text});

  TagList.fromJson(Map<String, dynamic> json) {
    text = json['text'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['text'] = this.text;
    return data;
  }
}

/// poi 卡片打车信息
class TcTaxiButtonDTO {
  bool? canUse;
  String? jumpUrl;

  TcTaxiButtonDTO({this.canUse, this.jumpUrl});

  TcTaxiButtonDTO.fromJson(Map<String, dynamic> json) {
    canUse = json['canUse'];
    jumpUrl = json['jumpUrl'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['canUse'] = this.canUse;
    data['jumpUrl'] = this.jumpUrl;
    return data;
  }
}
