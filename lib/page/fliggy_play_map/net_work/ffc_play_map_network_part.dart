part of '../../ffc_play_map_page.dart';

/// 地图主页面的网络请求部分
extension _PlayMapNetworkPart on _FfcPlayMapPageState {
  /// ============= data handle start ========
  /// 根据point查找playModel
  PlayInfoModel _findPlayModel(String markerTypeStr, CommonPointModel point) {
    List<PlayInfoModel> playInfoList = _playInfoMap[markerTypeStr] ?? [];
    PlayInfoModel target = playInfoList.firstWhere((playInfo) {
      return playInfo.bizId == point.pointBizId;
    }, orElse: () {
      return PlayInfoModel();
    });
    return target;
  }

  /// 更新玩法数据
  void _updatePlayInfoData(Map cards,{double? showLevel, bool? isSelect}) {
    // 1. 将Map转换为playInfoList
    Map<String, List<PlayInfoModel>> playData = cards.map((key, value) {
      List<PlayInfoModel> playCardList = [];
      List cardList = CommonSafeAccess.safeParseList(value);

      for (Map cardMap in cardList) {
        PlayInfoModel model = PlayInfoModel.fromJson(cardMap);
        model.tabId = (_selectedTabModel?.tabId ?? 0).toString() + (_selectedGroupModel?.tabId ?? 0).toString();

        if (showLevel!= null) {
          model.showLevel = showLevel;
        }
        if (isSelect!= null && isSelect) {
          model.isSelected = isSelect;
        }
        playCardList.add(model);
      }
      return MapEntry(key, playCardList);
    });
    // _playInfoMap = playData;
    // 合并操作
    playData.forEach((category, newItems) {
      var oldList =  _playInfoMap[category];
      if (oldList == null) {
        _playInfoMap[category] = newItems;
      } else {
        final existingIds = oldList.map((item) {
          if (item.contentId != null) {
            return item.contentId;
          } else if (item.poiId!= null) {
            return item.poiId;
          }
        }).toSet();

        final newItemsToAdd = newItems.where((element) {
          if (element.contentId!= null) {
            return !existingIds.contains(element.contentId);
          } else if (element.poiId!= null) {
            return !existingIds.contains(element.poiId);
          } else {
            return true;
          }
        });

        _playInfoMap[category] = [...oldList, ...newItemsToAdd];
      }
    });
  }

  /// 更新群聊数据
  void _updateChatGroupInfo(Map res) {
    Map chatGroupInfo = CommonSafeAccess.safeParseMap(res['chatGroup']);
    if (SafeAccess.isMapNotEmpty(chatGroupInfo)) {
      _chatGroupInfo.value = ChatGroupModel.fromJson(chatGroupInfo);
    } else {
      _chatGroupInfo.value = null;
    }
  }

  void _updatePointData() {
    // 生成地图所需的point list
    Map<String, List<CommonPointModel>> pointData =
    _playInfoMap.map((String key, List<PlayInfoModel> value) {
      List<CommonPointModel> pointList = [];
      for (PlayInfoModel playModel in value) {
        double? lat = playModel.latitude;
        double? log = playModel.longitude;
        if (lat != null && log != null) {
          CommonPointModel pointModel =
          CommonPointModel(LatLng(lat, log), pointBizId: playModel.bizId);
          pointList.add(pointModel);
        }
      }
      return MapEntry(key, pointList);
    });
    List<CommonPointGroupModel> pointGroupList = [];
    for (MapEntry<String, List<CommonPointModel>> entry in pointData.entries) {
      CommonPointGroupModel pointGroup =
      CommonPointGroupModel(entry.key, entry.value);
      if (pointGroup.bizType == MarkerTypeCards) {
        pointGroup.tempMarker = true;
      }
      pointGroupList.add(pointGroup);
    }
    _pointGroupList = pointGroupList;
  }

  /// ============= data handle end ========



  /// ============= request start ========
  /// 请求用户信息数据
  void _requestUserInfo() {
    TitleBarNet.requestUserInfo(context).then((value) {
      if (value.success) {
        // 先剔除接口中返回的tabInfo
        Map result = value.data['data'] ?? {};
        // 刷新用户头像数据
        safeSetState(() {
          Map userMap = CommonSafeAccess.safeParseMap(result['userInfo']);
          _userInfoModel = FFCUserInfoModel.fromJson(userMap);
        });
        // 更新Marker
        _mapController.currentState?.updateAllMarker();
      }
    }).catchError((error) {
      return new MtopResponseModel();
    });
  }

  /// 请求用户常住地逻辑
  Future<void> _requestPermanent() async {
    // 处理同/异城逻辑
    // if (_userLbs.value == null) return;
    await PlayMapNet.requestPermanent(context,
        userLat: _userLbs.value?.latitude ?? 0,
        userLng: _userLbs.value?.longitude ?? 0, onSucceed: (Map res) {
          _abroad = CommonSafeAccess.safeParseBoolean(res['abroad']) ?? false;
          _differentCity =
              CommonSafeAccess.safeParseBoolean(res['differentCity']) ?? false;
          _lbsCityId = CommonSafeAccess.safeParseNum(res['lbsCityId']);
          // 更新lbsMarker，让曝光埋点可以上报lbsCityId
          // _mapController.currentState?.updateMarkerGroup(_userPointGroup);
          Map permanentCity = CommonSafeAccess.safeParseMap(res['permanentCity']);
          if (permanentCity.isNotEmpty) {
            _permanentCity = CityModel.fromJson(permanentCity);
          }
        });
  }

  /// 请求玩法信息
  void _requestPlayInfo(LatLng centerLatLng, double zoom, double radius,
      {bool needUpdateCamera = false}) {
    // if (_selectedTabModel?.tabId == null && _selectedGroupModel?.tabId == null)
    //   return;
    // 组装请求参数
    Map params = {
      'centerLatitude': centerLatLng.latitude,
      'centerLongitude': centerLatLng.longitude,
      'mapLevel': zoom.round(),
      'radius': radius,
      'isFirstPage': _isFirst
    };
    List<num> categoryList = [];
    categoryList.safeAdd(_selectedTabModel?.tabId);
    categoryList.safeAdd(_selectedGroupModel?.tabId);
    params.safeAdd('categoryList', categoryList);

    double? originalLat = _userLbs.value?.latitude ?? _permanentCity?.latitude;
    double? originalLng =
        _userLbs.value?.longitude ?? _permanentCity?.longitude;
    params.safeAdd('latitude', originalLat);
    params.safeAdd('longitude', originalLng);

    List selectedIdList = [];
    List contentIdList = [];

    if (_isFirst) {
      // 首次进入,拼一下业务参数
      Map anchorMap = {};
      var anchoringCard = widget.params?['anchoring_id'];

      if (anchoringCard is Map ) {
        anchorMap = anchoringCard;
      } else if (anchoringCard is String) {
        anchorMap = jsonDecode(anchoringCard);
      }

      selectedIdList.addAll(extractAllIds(anchorMap));

      params.safeAdd('selectedContentIdList', selectedIdList);

      Map visibleMap = {};
      var visibleId = widget.params?['visible_id'];

      if (visibleId is Map ) {
        visibleMap = visibleId;
      } else if (visibleId is String) {
        visibleMap = jsonDecode(visibleId);
      }


      contentIdList.addAll(selectedIdList);
      contentIdList.addAll(extractAllIds(visibleMap));

      params.safeAdd('contentIdList', contentIdList);
    }


    // 发起请求
    debugPrint('muxian --- _requestPlayInfo start: $zoom');
    PlayMapNet.requestPlayInfo(context, params: params, onSucceed: (Map res) {
      debugPrint('muxian --- _requestPlayInfo end');
      Map newCardData = CommonSafeAccess.safeParseMap(res['cardData']);
      // 1.更新玩法数据
      _updatePlayInfoData(newCardData);
      // 2. 更新对应的point List
      _updatePointData();
      // 3. 如果当前请求是需要重置地图视角，则需要改变地图位置以展示玩法
      if (needUpdateCamera) {
        List<CommonPointModel> pointList =
        List<CommonPointModel>.from(_playInfoPointList);
        // 如果不是异地并且有用户lbs，则需要把用户lbs也展示出来
        if (!_differentCity) {
          pointList.safeAdd(_userPoint);
        }
        _mapController.currentState
            ?.updateCameraWithPoints(pointList: pointList, duration: 50, cameraPadding:
        SafeAccess.safeParseBoolean(widget.params?['back_enable']) ?
        EdgeInsets.only(top: 200) : (Platform.isIOS ? EdgeInsets.only(top: 100) : null));
      }
      // 4. 刷新地图Marker
      // Future.delayed(Duration(milliseconds: 300) ,() {
      // 切 tab 的时候会有一个移动动画,会影响碰撞计算
      _mapController.currentState?.updateAllMarker();
      // });
      // 5. 更新群聊内容
      _updateChatGroupInfo(res);
    }, onFailed: (mtopModel) {});

    _isFirst = false;
  }

/// ============= request end ========
}