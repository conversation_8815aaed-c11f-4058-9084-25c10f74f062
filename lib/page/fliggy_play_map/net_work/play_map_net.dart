import 'package:flutter/cupertino.dart';

import '../../../common/ffc_utils.dart';
import '../../../network/ffc_network.dart';

typedef RequestSucceed = Function(Map);
typedef RequestFailed = Function(MtopResponseModel);
typedef RequestFinally = Function();

class PlayMapNet extends FfcNetwork {
  static Future<void> requestPermanent(BuildContext context, {
    double? userLat,
    double? userLng,
    RequestSucceed? onSucceed,
    RequestFailed? onFailed
  }) async {
    Map arg = {
      if (userLat!= null) 'latitude': userLat,
      if (userLng!= null) 'longitude': userLng};
    MtopRequestModel requestModel = MtopRequestModel.buildRequset(
        api: 'mtop.fliggy.content.community.content.city.wide.profiles',
        version: '1.0',
        params: arg);
    MtopResponseModel mtopModel = await FliggyMtopApi.getInstance().send(context, requestModel);
    try {
      if(mtopModel.success) {
        Map result = mtopModel.data['data'] ?? {};
        _safeSucceed(onSucceed, result);
      } else {
        _safeFailed(onFailed, mtopModel);
      }
    } catch(e) {
      _safeFailed(onFailed, mtopModel);
    }
  }

  static Future<void> requestPlayInfo(BuildContext context, {
    required Map params,
    RequestSucceed? onSucceed,
    RequestFailed? onFailed,
    RequestFinally? onFinally
  }) async {
    MtopRequestModel requestModel = MtopRequestModel.buildRequset(
        api: 'mtop.fliggy.content.community.content.city.wide.page',
        version: '1.0',
        params: params);
    MtopResponseModel mtopModel = await FliggyMtopApi.getInstance().send(context, requestModel);
    try {
      if(mtopModel.success) {
        Map result = mtopModel.data['data'] ?? {};
        _safeSucceed(onSucceed, result);
      } else {
        _safeFailed(onFailed, mtopModel);
      }
    } catch(e) {
      _safeFailed(onFailed, mtopModel);
    } finally {
      _safeFinally(onFinally);
    }
  }

  static void _safeSucceed(RequestSucceed? onSucceed, Map result) {
    if(onSucceed != null) {
      onSucceed(result);
    }
  }

  static void _safeFailed(RequestFailed? onFailed, MtopResponseModel mtopModel) {
    if(onFailed != null) {
      onFailed(mtopModel);
    }
  }

  static void _safeFinally(RequestFinally? onFinally) {
    if(onFinally != null) {
      onFinally();
    }
  }
}