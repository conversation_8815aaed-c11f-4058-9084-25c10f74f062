import 'package:fliggy_flutter_community/common/collection_data_util.dart';
import 'package:fliggy_flutter_community/page/fliggy_play_map/utils/play_map_constant.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../components/ffc_custom_indicator.dart';
import '../../components/ffc_custom_tabbar.dart';
import 'package:fliggy_flutter_community/common/ffc_utils.dart';

import '../../model/ffc_user_info.dart';
import 'model/play_map_model.dart';

typedef OptionClicked = Function(PlayMapTabModel?, PlayMapTabModel?);

class PlayMapTabBar extends StatefulWidget {
  final List<PlayMapTabModel> tabs;
  final FFCUserInfoModel? userInfo;
  final CityModel? permanentCity;
  final Function()? onUserIconClicked;
  final OptionClicked? onTabClicked;
  final OptionClicked? onGroupClicked;

  final bool canBack;

  const PlayMapTabBar({
    required this.tabs,
    Key? key,
    this.userInfo,
    this.permanentCity,
    this.onUserIconClicked,
    this.onTabClicked,
    this.onGroupClicked,
    required this.canBack,
  }): super(key: key);

  @override
  State<PlayMapTabBar> createState() => _PlayMapTabBarState();
}

class _PlayMapTabBarState extends State<PlayMapTabBar>  with TickerProviderStateMixin{
  TabController? _tabController;
  ValueNotifier<TabColor> _tabStyle = ValueNotifier<TabColor>(TabColor());
  int _selectedTabIndex = 0;
  int _selectedGroupIndex = 0;

  PlayMapTabModel? get _selectedTab {
    PlayMapTabModel? currentTab = widget.tabs.safeElement(_selectedTabIndex);
    return currentTab;
  }

  List<PlayMapTabModel> get _groupList {
    return _selectedTab?.children ?? [];
  }

  PlayMapTabModel? get _selectedGroup {
    PlayMapTabModel? currentGroup = _groupList.safeElement(_selectedGroupIndex);
    return currentGroup;
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(initialIndex: _selectedTabIndex, length: widget.tabs.length, vsync: this);
    _tabStyle = ValueNotifier<TabColor>(
        TabColor(labelStyle: FfcTextStyle(
            fontFamily: 'PingFang SC',
            fontWeight: FontWeight.w600,
            fontSize: fd.font_size_large_title,
            color: fd.color_darkgray,
            height: 1.1),
        labelColor: fd.color_darkgray,
        unselectedLabelStyle: FfcTextStyle(
            fontFamily: 'PingFang SC',
            fontWeight: FontWeight.w400,
            fontSize: fd.font_size_middle_title,
            color: fd.color_darkgray,
            height: 1.1),
        unselectedLabelColor: fd.color_darkgray,
        indicatorColor: fd.color_darkgray
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 44),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [fd.color_white, fd.color_white, fd.color_white.withOpacity(0)], // 渐变颜色 fd.color_white
          stops: [0.0, 0.50, 1.0], //
          begin: Alignment.topCenter, // 渐变开始位置
          end: Alignment.bottomCenter, // 渐变结束位置
        )
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTabBarWrapper(),
          _buildGroupBar()
        ],
      ),
    );
  }

  Widget _buildTabBarWrapper() {
    return Container(
      margin: EdgeInsets.only(right: 12),
      // color: Colors.red,
      child: Row(
        children: [
          if (widget.canBack)
            GestureDetector(
              onTap: () {
                FliggyNavigatorApi.getInstance().pop(context);
              },
              child: Container(
                height: 20,
                width: 30,
                alignment: Alignment.centerRight,
                color: Color(0x00FFFFFF),
                child: Icon(
                  IconData(
                    0xe961,
                    fontFamily: FONT_FAMILY_DEFAULT,
                    fontPackage: 'ficonfont',
                  ),
                  size: 20,
                  color: Color(0xFF0F131A),
                  weight: 700,
                ),
              ),
            ),
          Expanded(
            // color: Colors.green,
            child: Stack(
              children: [
                _buildTabBar(),
                if(widget.tabs.length >= 4)
                  IgnorePointer(
                    child:
                    Align(
                        alignment: Alignment.centerRight,
                        child: FRoundImage.network(
                          'https://gw.alicdn.com/imgextra/i3/O1CN01Msvu631zQJpceFWbZ_!!6000000006708-2-tps-18-77.png',
                          height: 38,
                        )
                    ),
                  )
              ],
            ),
          ),
          Spacer(),
          _buildUserIcon()
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return TabBar(
      isScrollable: true,
      controller: _tabController,
      padding: EdgeInsets.only(left: 0),
      labelStyle: _tabStyle.value.labelStyle,
      unselectedLabelStyle: _tabStyle.value.unselectedLabelStyle,
      labelColor: _tabStyle.value.labelColor,
      unselectedLabelColor: _tabStyle.value.unselectedLabelColor,
      labelPadding: EdgeInsets.all(7),
      tabs: _tabItemWidgets(),
      indicator: FfcCustomIndicator(
          width: 27,
          height: 3,
          alignment: Alignment.bottomCenter,
          radius: BorderRadius.circular(1.5),
          padding: const EdgeInsets.only(bottom: 3),
          color: _tabStyle
      ),
      onTap: (int selectedIndex) {
        if(_selectedTabIndex == selectedIndex) return;

        _selectedTabIndex = selectedIndex;
        _selectedGroupIndex = 0;
        if(widget.onTabClicked != null) {
          widget.onTabClicked!(_selectedTab, _selectedGroup);
        }
        setState(() {
        });
        // 点击埋点
        FfcUtils.trackClickPlayMap(context, 'category_bar.category_${_selectedTab?.tabId}', 'play_map_tab_category');
        _tabController?.animateTo(_selectedTabIndex);

      },
    );
  }

  List<Widget> _tabItemWidgets() {
    List<PlayMapTabModel> tabModelList = widget.tabs.map((model) => model).toList();
    List<Widget> tabWidgets = [];
    for(PlayMapTabModel tabModel in tabModelList) {
      if(tabModel.tabName.isNotEmpty) {
        tabWidgets.add(_buildTabItem(tabModel));
      }
    }
    return tabWidgets;
  }

  Widget _buildTabItem(PlayMapTabModel tabModel) {
    return FfcExposeWidget(
        controllerName: 'play_map_tab_category',
        spmAB: PlayMapConstant.spm_AB,
        spmCD: 'category_bar.category_${tabModel.tabId}',
        child: Container(
          // color: Colors.yellow,
          height: 25,
          child: Align(
            alignment: Alignment.bottomCenter,
            child: Text(tabModel.tabName ?? ''),
          ),
        ));
  }

  Widget _buildUserIcon() {
    return IconButton(
      padding: EdgeInsets.all(0),
      iconSize: 28,
      icon: FutureBuilder<bool>(
        future: FfcUtils.hasLogin(),
        builder: (context, snapshot) {
          return _buildUserInfo(snapshot.data ?? false);
        },
      ),
      onPressed: () async {
        if(widget.onUserIconClicked != null) {
          widget.onUserIconClicked!();
        }
      },
    );
  }

  Widget _buildUserInfo(bool isLogin) {

    return FfcExposeWidget(
      key: Key(isLogin ? 'usericon' : 'login_btn'),
      controllerName: isLogin ? 'usericon' : 'login_btn',
      spmAB: PlayMapConstant.spm_AB,
      spmCD: 'category_bar.usericon',
      params: {'city_id': widget.permanentCity?.cityId ?? ''},
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          isLogin ? Container(
            width: 22,
            height: 22,
            margin: const EdgeInsets.symmetric(vertical: 1),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(11),
              border: Border.all(
                color: Color(0x33000000),
                width: 1,
              ),
              image: DecorationImage(
                image: FfcCacheNetWorkImageProvider(widget.userInfo?.avatar ?? ''),
                fit: BoxFit.cover,
              ),
            ),
          ) : Container(
            margin: const EdgeInsets.symmetric(vertical: 1),
            child: FRoundImage.base64(FfcDataUtils.loginIcon,
              width: 22,
              height: 22,
            ),
          ),
          Text(
            isLogin ? '个人主页' : '请登录',
            style: FfcTextStyle(
                fontSize: fd.font_size_text_4,
                color: fd.color_darkgray),
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  Widget _buildGroupBar() {
    return Container(
      margin: EdgeInsets.only(top: 9),
      height: 44,
      child: _groupList.isEmpty ?
      SizedBox.shrink() :
      ListView.builder(
          itemBuilder: (BuildContext context, int index) {
            PlayMapTabModel groupModel = _groupList[index];
            bool selected = _selectedGroupIndex == index;
            return _buildGroupItem(groupModel, index, selected);
          },
        scrollDirection: Axis.horizontal,
        itemCount: _groupList.length,
        padding: EdgeInsets.only(left: 3, right: 12),
        shrinkWrap: true,
      ),
    );
  }


  Widget _buildGroupItem(PlayMapTabModel groupModel, int index, bool selected) {
    Widget textWidget = Text(groupModel.tabName ?? '',
      style: TextStyle(
          fontSize: 12,
          fontWeight: selected ? FontWeight.w600 : FontWeight.w400
      ),
    );

    return GestureDetector(
      onTap: () {
        if(_selectedGroupIndex == index) return;

        _selectedGroupIndex = index;

        if(widget.onGroupClicked != null) {
          widget.onGroupClicked!(_selectedTab, _selectedGroup);
        }

        safeSetState(() {

        });
        // 点击埋点
        FfcUtils.trackClickPlayMap(context, 'category2_bar.category2_${_selectedGroup?.tabId}', 'play_map_tab_category2');

      },
      child: FfcExposeWidget(
        controllerName: 'play_map_tab_category2',
        spmAB: PlayMapConstant.spm_AB,
        spmCD: 'category2_bar.category2_${groupModel.tabId}',
        child: _buildGroupContainer(textWidget, selected),
      ),
    );
  }

  Widget _buildGroupContainer(Widget text, bool selected) {
    if(_groupList.length <= 4) {
      return Align(
        alignment: Alignment.topCenter,
        child: Container(
          height: 32,
          width: 81,
          margin: EdgeInsets.only(left: 9),
          decoration: BoxDecoration(
              color: selected ? fd.color_brand_3 : fd.color_white,
              border: Border.all(color: fd.color_white, width: 1.5),
              borderRadius: BorderRadius.all(Radius.circular(18)),
              boxShadow: [
                BoxShadow(
                  color: Color(0xFF3A4F6A).withOpacity(0.1), // 阴影颜色
                  spreadRadius: 0, // 阴影扩散半径
                  blurRadius: 10, // 模糊半径
                  offset: Offset(0, 3), // 阴影偏移量 (x, y)
                ),
              ]
          ),
          child: Center(
            child: text,
          ),
        ),
      )
        ;
    } else {
      return Align(
        alignment: Alignment.topCenter,
        child: Container(
          height: 32,
          margin: EdgeInsets.only(left: 9),
          padding: EdgeInsets.symmetric(horizontal: 15),
          decoration: BoxDecoration(
              color: selected ? fd.color_brand_3 : fd.color_white,
              border: Border.all(color: fd.color_white, width: 1.5),
              borderRadius: BorderRadius.all(Radius.circular(18)),
              boxShadow: [
                BoxShadow(
                  color: Color(0xFF3A4F6A).withOpacity(0.1), // 阴影颜色
                  spreadRadius: 0, // 阴影扩散半径
                  blurRadius: 10, // 模糊半径
                  offset: Offset(0, 3), // 阴影偏移量 (x, y)
                ),
              ]
          ),
          child: Center(
            child: text,
          ),
        ),
      )
        ;
    }
  }
}
