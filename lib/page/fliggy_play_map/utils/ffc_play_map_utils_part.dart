part of '../../ffc_play_map_page.dart';

/// 地图主页面的网络请求部分
extension _PlayMapUtilsPart on _FfcPlayMapPageState {
  // 通用递归查找方法
  List<dynamic> extractAllIds(dynamic data) {
    final List<dynamic> ids = [];

    void recursiveFind(dynamic node) {
      if (node is Map) {
        // 检查当前map是否包含目标字段
        _checkAndAdd(node, 'contentId', ids);
        _checkAndAdd(node, 'poiId', ids);

        // 递归处理所有子值
        node.values.forEach((element) {
          if (element is Map || element is List) {
            recursiveFind(element);
          }
        });
      } else if (node is List) {
        // 递归处理列表元素
        node.forEach((element) {
          if (element is Map || element is List) {
            recursiveFind(element);
          }
        });
      }
    }

    recursiveFind(data);
    return ids;
  }

  void _checkAndAdd(Map<dynamic, dynamic> map, String key, List<dynamic> ids) {
    if (map.containsKey(key) && map[key] != null) {
      ids.add(map[key]);
    }
  }

  LatLng? _getFirstLatLng(Map data) {
    // 1. 验证数据结构
    if (data.isEmpty) {
      return null;
    }

    // 2. 获取第一个键（无论名称是什么）
    final firstKey = data.keys.first;
    final dynamic value = data[firstKey];

    if (value is Map) {
      if (value.containsKey('latitude') && value.containsKey('longitude')) {
        final lat = SafeAccess.safeParseDouble(value['latitude']);
        final lng = SafeAccess.safeParseDouble(value['longitude']);
        return LatLng(lat, lng);
      }
      return null;
    }
    // 3. 验证值是否为 List 类型
    if (value is! List) {
      return null;
    }

    // 4. 验证列表非空
    if (value.isEmpty) {
      return null;
    }

    // 5. 获取第一个元素
    final firstItem = value.first;

    // 6. 验证元素格式
    if (firstItem is! Map<String, dynamic>) {
      return null;
    }

    // 7. 安全获取坐标值
    try {
      if (firstItem.containsKey('latitude') &&
          firstItem.containsKey('longitude')) {
        final lat = SafeAccess.safeParseDouble(firstItem['latitude']);
        final lng = SafeAccess.safeParseDouble(firstItem['longitude']);
        return LatLng(lat, lng);
      }
      return null;
    } catch (e) {
      return null;
    }
  }
}
