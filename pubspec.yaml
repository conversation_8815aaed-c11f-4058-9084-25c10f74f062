name: fliggy_flutter_community
description: A new Flutter plugin.
version: 0.0.1
author:
homepage:
# 应用名称，如 fliggy，影响keep同步及依赖版本号校验
app_name: fliggy
# 应用基线版本号，影响keep同步及依赖版本号校验
baseline: ***********
# 开启或关闭自动同步依赖。不写该参数，默认开启
sync_dep: true

environment:
  sdk: ">=2.12.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  # 无尽流实现方案
  # 无意见、可扩展和高度可定制的软件包，帮助您在用户向下滚动屏幕时懒惰地加载和显示小块项目——称为无限滚动分页、无尽滚动分页、自动分页、懒惰加载分页、渐进式加载分页等。
  infinite_scroll_pagination: 4.0.0 # All
  fround_image:
    git:
      url: **************************:fapi/fround_image.git
      ref: 0.6.7
  fbridge:
    git:
      url: **************************:fliggy_mobile/fbridge.git
      ref: 3.2.22

#  cached_network_image: 3.2.3

dependency_overrides:
  # ----------------------  动态依赖 ----------------------
  flutter_boost:
    git:
      ref: 4.4.9
      url: **************************:fliggy_mobile/flutter_boost.git
  shared_preferences:
    git:
      ref: 2.0.8
      url: **************************:fapi/shared_preferences.git
  flutter_common:
    git:
      ref: 1.6.0
      url: **************************:fliggy_mobile/flutter_common.git
#  fbridge:
  fbridge_channel:
    git:
      ref: 1.2.0
      url: **************************:fliggy_mobile/fbridge_channel.git
  fliggy_mtop:
    git:
      ref: 1.2.9
      url: **************************:fliggy_mobile/fliggy_mtop.git
  fliggy_usertrack:
    git:
      ref: 2.0.6
      url: **************************:fliggy_mobile/fliggy_usertrack.git
  fliggy_router:
    git:
      url: **************************:fliggy_mobile/fliggy_router.git
      ref: 2.1.22
  fimage:
    git:
      url: **************************:fapi/fimage.git
      ref: 1.0.2
  fbroadcast:
    git:
      ref: 1.3.0
      url: **************************:fapi/fbroadcast.git
  fdata_center:
    git:
      url: **************************:fapi/fdata_center.git
      ref: 0.0.4
  fsuper:
    git:
      ref: 2.1.2
      url: **************************:fapi/fsuper.git
  ftoast:
    git:
      ref: 2.0.1
      url: **************************:fapi/ftoast.git
  fliggy_design: 0.0.4
  fround_image:
    git:
      ref: 0.6.7
      url: **************************:fapi/fround_image.git
  archive:
    git:
      ref: 3.3.1
      url: **************************:fliggy_mobile/archive.git
  fliggy_flutter_location:
    git:
      ref: 0.2.4
      url: **************************:fliggy_mobile/fliggy_location.git
  cached_network_image: 3.2.3
  flutter_cache_manager: 3.3.0
  ffperformance:
    git:
      ref: 2.1.7
      url: **************************:fliggy_mobile/ffperformance.git
  package_info_plus:
    git:
      ref: 3598ce68def47fea89afeeea66343ae848012a87
      url: **************************:fliggy_mobile/package_info_plus.git
  fliggy_flutter_login:
    git:
      ref: 0.2.1
      url: **************************:fliggy_mobile/fliggy_login.git
  pull_to_refresh: 2.0.0
  ficonfont:
    git:
      ref: 1.3.3
      url: **************************:fliggy_mobile/ficonfont.git
  fdensity:
    git:
      ref: 2.1.2
      url: **************************:fapi/fdensity.git
  fliggy_app_info:
    git:
      ref: 1.0.7
      url: **************************:fliggy_mobile/fliggy_app_info.git
  ftitlebar:
    git:
      ref: 1.1.1
      url: **************************:fapi/ftitlebar.git
  ftitlebar_adapter:
    git:
      ref: 1.1.3
      url: **************************:fliggy_mobile/ftitlebar_adapter.git
  ffliggykv_core:
    git:
      ref: 2.0.5
      url: **************************:fliggy_mobile/ffliggykv_core.git
  flutter_fliggyKV:
    git:
      ref: 2.0.5
      url: **************************:fliggy_mobile/flutter_fliggyKV.git
  # ----------------------  外部依赖 ----------------------
  fliggy_webview:
    git:
      url: **************************:fliggy_mobile/fliggy_webview.git
      ref: 0.3.4
  fbridge:
    git:
      url: **************************:fliggy_mobile/fbridge.git
      ref: 3.2.22
  fexp:
    git:
      url: **************************:fapi/fexp.git
      ref: master
  flutter_staggered_grid_view:
    git:
      url: **************************:fapi/flutter_staggered_grid_view.git
      ref: master
  html: 0.15.0
  lottie: 2.3.2
  dio: 4.0.6
  quiver: 3.2.1
  infinite_scroll_pagination:
    git:
      url: **************************:fapi/infinite_scroll_pagination.git
      ref: 4.0.0
  fliggy_flutter_map:
#    path: /Users/<USER>/Fliggy/Fliggy_flutter/fliggy_flutter_map
    git:
      url: **************************:fliggy_mobile/fliggy_flutter_map.git
      ref: 0.4.24
  path_provider: 2.0.6
  tuple: 2.0.1
  intl: 0.17.0
  fcontrol:
    git:
      url: "**************************:fapi/fcontrol.git"
      ref: "2.0.1"
  fliggy_font:
    git:
      url: **************************:fliggy_mobile/fliggy_design.git
      ref: 0.2.0
      path: packages/fliggy_font
#  fluttertoast: 8.0.9
dev_dependencies:
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
# This section identifies this Flutter project as a plugin project.
# The androidPackage and pluginClass identifiers should not ordinarily
# be modified. They are used by the tooling to maintain consistency when
# adding or updating assets for this project.

# 有Native代码再打开！
# 有Native代码再打开！
# 有Native代码再打开！
#  plugin:
#    androidPackage: com.taobao.trip.fliggy_flutter_community
#    pluginClass: FliggyFlutterCommunityPlugin

## To add assets to your plugin package, add an assets section, like this:
  assets:
    - assets/play_map/iOS/style.data
    - assets/play_map/iOS/style_extra.data
    - assets/play_map/Android/style.data
    - assets/play_map/Android/style_extra.data
    - assets/play_map/iOS_10/style.data
    - assets/play_map/iOS_10/style_extra.data
    - assets/play_map/Android_10/style.data
    - assets/play_map/Android_10/style_extra.data
    - assets/play_map/user_location_icon.png
#
# For details regarding assets in packages, see
# https://flutter.dev/assets-and-images/#from-packages
#
# An image asset can refer to one or more resolution-specific "variants", see
# https://flutter.dev/assets-and-images/#resolution-aware.

# To add custom fonts to your plugin package, add a fonts section here,
# in this "flutter" section. Each entry in this list should have a
# "family" key with the font family name, and a "fonts" key with a
# list giving the asset and other descriptors for the font. For
# example:
# fonts:
#   - family: Schyler
#     fonts:
#       - asset: fonts/Schyler-Regular.ttf
#       - asset: fonts/Schyler-Italic.ttf
#         style: italic
#   - family: Trajan Pro
#     fonts:
#       - asset: fonts/TrajanPro.ttf
#       - asset: fonts/TrajanPro_Bold.ttf
#         weight: 700
#
# For details regarding fonts in packages, see
# https://flutter.dev/custom-fonts/#from-packages
