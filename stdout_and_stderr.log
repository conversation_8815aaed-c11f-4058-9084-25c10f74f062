Running "flutter pub get" in fliggy_flutter_community...
Resolving dependencies...
  args 2.4.2 (2.7.0 available)
  asn1lib 1.4.1 (1.6.4 available)
  async 2.10.0 (2.13.0 available)
  badges 3.1.1 (3.1.2 available)
  boolean_selector 2.1.1 (2.1.2 available)
! cached_network_image 3.2.3 (overridden) (3.4.1 available)
  cached_network_image_platform_interface 2.0.0 (4.1.1 available)
  cached_network_image_web 1.0.2 (1.3.1 available)
  characters 1.2.1 (1.4.0 available)
  clock 1.1.1 (1.1.2 available)
  collection 1.17.0 (1.19.1 available)
  convert 3.1.1 (3.1.2 available)
  crypto 3.0.2 (3.0.6 available)
  csslib 0.17.3 (1.0.2 available)
! dio 4.0.6 (overridden) (5.8.0+1 available)
  encrypt 5.0.0 (5.0.3 available)
  fake_async 1.3.1 (1.3.3 available)
  ffi 2.0.2 (2.1.4 available)
  file 6.1.4 (7.0.1 available)
  fliggy_price 0.0.8 (0.0.10 available)
  flutter_blurhash 0.7.0 (0.9.1 available)
! flutter_cache_manager 3.3.0 (overridden) (3.4.1 available)
  flutter_plugin_android_lifecycle 2.0.17 (2.0.28 available)
  fpdart 0.6.0 (1.1.1 available)
! html 0.15.0 (overridden) (0.15.6 available)
  http 0.13.6 (1.4.0 available)
  http_parser 4.0.2 (4.1.2 available)
  image 3.1.1 (4.5.4 available)
! intl 0.17.0 (overridden) (0.20.2 available)
  js 0.6.5 (0.7.2 available)
! lottie 2.3.2 (overridden) (3.3.1 available)
  matcher 0.12.13 (0.12.17 available)
  material_color_utilities 0.2.0 (0.12.0 available)
  meta 1.8.0 (1.17.0 available)
  octo_image 1.0.2 (2.1.0 available)
  package_info_plus_platform_interface 2.0.1 (3.2.0 available)
  path 1.8.2 (1.9.1 available)
! path_provider 2.0.6 (overridden) (2.1.5 available)
  path_provider_platform_interface 2.1.1 (2.1.2 available)
  path_provider_windows 2.2.1 (2.3.0 available)
  petitparser 5.1.0 (6.1.0 available)
  platform 3.1.3 (3.1.6 available)
  plugin_platform_interface 2.1.6 (2.1.8 available)
  pointycastle 3.7.3 (4.0.0 available)
  qr 2.1.0 (3.0.2 available)
  qr_flutter 4.0.0 (4.1.0 available)
! quiver 3.2.1 (overridden) (3.2.2 available)
  rxdart 0.27.7 (0.28.0 available)
  shared_preferences_linux 2.3.2 (2.4.1 available)
  shared_preferences_platform_interface 2.3.1 (2.4.1 available)
  shared_preferences_web 2.2.1 (2.4.3 available)
  shared_preferences_windows 2.3.2 (2.4.1 available)
  source_span 1.9.1 (1.10.1 available)
  sqflite 2.2.8+4 (2.4.2 available)
  sqflite_common 2.4.5+1 (2.5.5 available)
  stack_trace 1.11.0 (1.12.1 available)
  stream_channel 2.1.1 (2.1.4 available)
  stream_transform 2.1.0 (2.1.1 available)
  string_scanner 1.2.0 (1.4.1 available)
  synchronized 3.1.0 (3.3.1 available)
  term_glyph 1.2.1 (1.2.2 available)
  test_api 0.4.16 (0.7.6 available)
! tuple 2.0.1 (overridden) (2.0.2 available)
  typed_data 1.3.2 (1.4.0 available)
  url_launcher 6.0.10 (6.3.1 available)
  url_launcher_linux 2.0.3 (3.2.1 available)
  url_launcher_macos 2.0.3 (3.2.2 available)
  url_launcher_platform_interface 2.2.0 (2.3.2 available)
  url_launcher_web 2.0.19 (2.4.1 available)
  url_launcher_windows 2.0.2 (3.1.4 available)
  uuid 3.0.7 (4.5.1 available)
  vector_math 2.1.4 (2.2.0 available)
  win32 4.1.4 (5.13.0 available)
  xdg_directories 1.0.3 (1.1.0 available)
  xml 5.4.1 (6.5.0 available)
Warning: You are using these overridden dependencies:
! archive 3.3.1 <NAME_EMAIL>:fliggy_mobile/archive.git at 0227a2
! cached_network_image 3.2.3 (3.4.1 available)
! dio 4.0.6 (5.8.0+1 available)
! fbridge 3.2.21 <NAME_EMAIL>:fliggy_mobile/fbridge.git at 2c04aa
! fbridge_channel 1.2.0 <NAME_EMAIL>:fliggy_mobile/fbridge_channel.git at cc9ae9
! fbroadcast 1.3.0 <NAME_EMAIL>:fapi/fbroadcast.git at 1d6c95
! fcontrol 2.0.1 <NAME_EMAIL>:fapi/fcontrol.git at 8ec56a
! fdata_center 0.0.4 <NAME_EMAIL>:fapi/fdata_center.git at 3caf1d
! fdensity 2.1.2 <NAME_EMAIL>:fapi/fdensity.git at 3dc707
! fexp 1.0.0 <NAME_EMAIL>:fapi/fexp.git at 403de3
! ffliggykv_core 2.0.5 <NAME_EMAIL>:fliggy_mobile/ffliggykv_core.git at 52db31
! ffperformance 2.1.0 <NAME_EMAIL>:fliggy_mobile/ffperformance.git at 6e618e
! ficonfont 1.3.0 <NAME_EMAIL>:fliggy_mobile/ficonfont.git at dd23c4
! fimage 1.0.2 <NAME_EMAIL>:fapi/fimage.git at e38a9e
! fliggy_app_info 1.0.4 <NAME_EMAIL>:fliggy_mobile/fliggy_app_info.git at 45a22a
! fliggy_design 0.0.4
! fliggy_flutter_location 0.2.4 <NAME_EMAIL>:fliggy_mobile/fliggy_location.git at 5f2733
! fliggy_flutter_login 0.1.3 <NAME_EMAIL>:fliggy_mobile/fliggy_login.git at 4484fa
! fliggy_flutter_map 1.0.0 <NAME_EMAIL>:fliggy_mobile/fliggy_flutter_map.git at b16238
! fliggy_font 0.0.1 <NAME_EMAIL>:fliggy_mobile/fliggy_design.git at 569593 in packages/fliggy_font
! fliggy_mtop 1.2.9 <NAME_EMAIL>:fliggy_mobile/fliggy_mtop.git at e00ff9
! fliggy_router 2.1.22 <NAME_EMAIL>:fliggy_mobile/fliggy_router.git at db61d8
! fliggy_usertrack 2.0.0 <NAME_EMAIL>:fliggy_mobile/fliggy_usertrack.git at 791fc6
! fliggy_webview 0.3.2 <NAME_EMAIL>:fliggy_mobile/fliggy_webview.git at 3b11ca
! flutter_boost 4.4.5 <NAME_EMAIL>:fliggy_mobile/flutter_boost.git at 6a367b
! flutter_cache_manager 3.3.0 (3.4.1 available)
! flutter_common 1.6.0 <NAME_EMAIL>:fliggy_mobile/flutter_common.git at c5b18b
! flutter_fliggyKV 2.0.5 <NAME_EMAIL>:fliggy_mobile/flutter_fliggyKV.git at f85de2
! flutter_staggered_grid_view 0.6.2 <NAME_EMAIL>:fapi/flutter_staggered_grid_view.git at d055db
! fround_image 0.6.7 <NAME_EMAIL>:fapi/fround_image.git at e592d1
! fsuper 2.1.2 <NAME_EMAIL>:fapi/fsuper.git at 65ea5b
! ftitlebar 1.1.0 <NAME_EMAIL>:fapi/ftitlebar.git at ca50d7
! ftitlebar_adapter 1.1.2 <NAME_EMAIL>:fliggy_mobile/ftitlebar_adapter.git at 8f80e3
! ftoast 1.0.0 <NAME_EMAIL>:fapi/ftoast.git at 52372d
! html 0.15.0 (0.15.6 available)
! infinite_scroll_pagination 4.0.0 <NAME_EMAIL>:fapi/infinite_scroll_pagination.git at 8d4c34
! intl 0.17.0 (0.20.2 available)
! lottie 2.3.2 (3.3.1 available)
! package_info_plus 4.0.0 <NAME_EMAIL>:fliggy_mobile/package_info_plus.git at 3598ce
! path_provider 2.0.6 (2.1.5 available)
! pull_to_refresh 2.0.0
! quiver 3.2.1 (3.2.2 available)
! shared_preferences 2.0.8 <NAME_EMAIL>:fapi/shared_preferences.git at 4de85d
! tuple 2.0.1 (2.0.2 available)
Got dependencies!
